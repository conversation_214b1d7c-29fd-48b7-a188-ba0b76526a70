// 归途游戏
function useRunnerGame(canvasContainerRef, onGameEnd) {
    // 游戏配置
    const CONFIG = {
        // 纹理路径
        textures: {
            background: 'img/bj2.jpg',
            road1: 'img/wall1.jpg',//场景1地面图片
            road2: 'img/wall2.jpg',//场景2地面图片
            road3: 'img/wall3.jpg',//场景3地面图片
            player: 'img/player.png'
        },

        // 场景1街道模型
        streetModel1: {
            objPath: 'mould/street1/base.obj',
            textures: {
                diffuse: 'mould/street1/texture_diffuse.jpg',
            },
            leftStreetCount: 7,
            rightStreetCount: 7,
            roadSegmentCount: 5
        },
        // 场景2街道模型
        streetModel2: {
            objPath: 'mould/street2/base.obj',
            textures: {
                diffuse: 'mould/street2/texture_diffuse.jpg',
            },
            leftStreetCount: 7,
            rightStreetCount: 7,
            roadSegmentCount: 5
        },
        // 场景3街道模型
        streetModel3: {
            objPath: 'mould/street3/base.obj',
            textures: {
                diffuse: 'mould/street3/texture_diffuse.jpg',
            },
            leftStreetCount: 7,
            rightStreetCount: 7,
            roadSegmentCount: 5
        },

        // 3D城门模型配置
        cityGateModel: {
            objPath: 'mould/door/base.obj',
            textures: {
                diffuse: 'mould/door/texture_diffuse.jpg',
            }
        },

        // 高度配置
        cityGateHeight: 30,
        streetHeight: 18,
        playerHeight: 2,

        // 位置配置
        cityGateDistance: 100,
        roadWidth: 6,
        sideGroundExtension: 50,

        // 摄像机配置
        cameraDistance: 2,
        cameraHeight: 4.3,
        cameraAngle: 120,
        cameraLookAheadDistance: 100
    };

    // 游戏状态
    const gameState = ref({
        isRunning: false,
        isGameOver: false,
        coins: 0,
        distance: 0,
        fps: 0,
        showGameOver: false,
        finalScore: 0,
        finalDistance: 0,
        gameStartTime: 0
    });

    // 游戏变量
    let scene, camera, renderer;
    let player, road;
    const gameObjects = ref([]);
    const sceneryObjects = ref([]);
    let gameStateInternal = 'loading'; // loading, playing, gameOver
    let playerSpeed = 1;
    let score = 0;
    let distance = 0;
    let keys = {};
    const DEBUG_LOG = false;
    let clock = new THREE.Clock();
    let accumulator = 0;
    const FIXED_DT = 1 / 60;
    let backgroundPlaneWidth = 15;

    // 模型加载状态跟踪
    let modelsLoaded = {
        street: false,
        cityGate: false,
        road: false,
        player: false
    };

    // 玩家状态
    let playerX = 0;
    let playerZ = 0;

    // 障碍间距控制
    const obstacleSpawnBaseProb = 0.10;
    const baseObstacleGap = 60;
    let lastObstacleZ = -Infinity;
    const baseCoinGap = 12;
    let lastCoinZ = -Infinity;
    const MAX_COINS = 60;
    const MAX_OBSTACLES = 40;
    let lastSpawnZ = 0;

    // 共享几何与材质
    const shared = {
        coinGeometry: new THREE.CylinderGeometry(0.5, 0.5, 0.2, 8),
        coinMaterial: new THREE.MeshLambertMaterial({ color: 0xFFD700 }),
        obstacleGeometry: new THREE.BoxGeometry(1.5, 1.5, 1.5),
        obstacleMaterial: new THREE.MeshLambertMaterial({ color: 0x555555 }),
        treeTrunkGeometry: new THREE.CylinderGeometry(0.3, 0.4, 3, 8),
        treeTrunkMaterial: new THREE.MeshLambertMaterial({ color: 0x8B4513 }),
        treeLeavesGeometry: new THREE.SphereGeometry(1.5, 8, 8),
        treeLeavesMaterial: new THREE.MeshLambertMaterial({ color: 0x228B22 }),
        houseBodyGeometry: new THREE.BoxGeometry(3, 3, 3),
        houseBodyMaterial: new THREE.MeshLambertMaterial({ color: 0x8B4513 }),
        houseRoofGeometry: new THREE.ConeGeometry(2.2, 1.5, 4),
        houseRoofMaterial: new THREE.MeshLambertMaterial({ color: 0xDC143C }),
        signPoleGeometry: new THREE.CylinderGeometry(0.1, 0.1, 3, 8),
        signPoleMaterial: new THREE.MeshLambertMaterial({ color: 0x8B4513 }),
        signBoardGeometry: new THREE.BoxGeometry(2, 1.5, 0.2),
        signBoardMaterial: new THREE.MeshLambertMaterial({ color: 0x4169E1 })
    };
    const tempLookAt = new THREE.Vector3();

    // 检查所有模型是否加载完成
    function checkAllModelsLoaded() {
        const allLoaded = Object.values(modelsLoaded).every(loaded => loaded);
        if (allLoaded && gameStateInternal === 'loading') {
            console.log('所有模型加载完成，开始游戏');
            gameStateInternal = 'playing';
            gameState.value.isRunning = true;
            gameState.value.gameStartTime = Date.now();

            // 调用loadAssets的resolve
            if (window.loadAssetsResolve) {
                window.loadAssetsResolve();
                window.loadAssetsResolve = null;
            }
        }
    }

    // 标记模型加载完成
    function markModelLoaded(modelName) {
        modelsLoaded[modelName] = true;
        checkAllModelsLoaded();
    }

    // 初始化Three.js场景
    function initThreeJS() {
        // 创建场景
        scene = new THREE.Scene();

        // 创建摄像机
        camera = new THREE.PerspectiveCamera(CONFIG.cameraAngle, window.innerWidth / window.innerHeight, 0.1, 1000);

        // 创建渲染器
        renderer = new THREE.WebGLRenderer({ antialias: true, alpha: true });
        renderer.setSize(window.innerWidth, window.innerHeight);
        renderer.setClearColor(0x000000, 0);
        renderer.setPixelRatio(Math.min(window.devicePixelRatio || 1, 2));
        renderer.shadowMap.enabled = false;

        // 将渲染器添加到指定的容器
        if (canvasContainerRef.value) {
            canvasContainerRef.value.appendChild(renderer.domElement);
        }

        // 添加光源
        const ambientLight = new THREE.AmbientLight(0xffffff, 0.6);
        scene.add(ambientLight);

        const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
        directionalLight.position.set(10, 20, 5);
        scene.add(directionalLight);
    }

    // 简化的OBJ加载器
    class SimpleOBJLoader {
        constructor() {
            this.textureLoader = new THREE.TextureLoader();
        }

        async loadOBJ(objPath) {
            try {
                const response = await fetch(objPath);
                const objText = await response.text();
                return this.parseOBJ(objText);
            } catch (error) {
                throw new Error(`Failed to load OBJ file: ${error.message}`);
            }
        }

        parseOBJ(objText) {
            const vertices = [];
            const normals = [];
            const uvs = [];
            const faces = [];

            const lines = objText.split('\n');

            for (let line of lines) {
                line = line.trim();
                if (line.startsWith('v ')) {
                    const parts = line.split(/\s+/);
                    vertices.push(
                        parseFloat(parts[1]),
                        parseFloat(parts[2]),
                        parseFloat(parts[3])
                    );
                } else if (line.startsWith('vn ')) {
                    const parts = line.split(/\s+/);
                    normals.push(
                        parseFloat(parts[1]),
                        parseFloat(parts[2]),
                        parseFloat(parts[3])
                    );
                } else if (line.startsWith('vt ')) {
                    const parts = line.split(/\s+/);
                    uvs.push(
                        parseFloat(parts[1]),
                        parseFloat(parts[2])
                    );
                } else if (line.startsWith('f ')) {
                    const parts = line.split(/\s+/).slice(1);
                    const face = [];
                    for (let part of parts) {
                        const indices = part.split('/');
                        face.push({
                            vertex: parseInt(indices[0]) - 1,
                            uv: indices[1] ? parseInt(indices[1]) - 1 : null,
                            normal: indices[2] ? parseInt(indices[2]) - 1 : null
                        });
                    }
                    faces.push(face);
                }
            }

            // 创建Three.js几何体
            const geometry = new THREE.BufferGeometry();
            const positions = [];
            const normalsArray = [];
            const uvsArray = [];

            for (let face of faces) {
                if (face.length >= 3) {
                    // 处理三角形
                    for (let i = 0; i < 3; i++) {
                        const vertexIndex = face[i].vertex;
                        positions.push(
                            vertices[vertexIndex * 3],
                            vertices[vertexIndex * 3 + 1],
                            vertices[vertexIndex * 3 + 2]
                        );

                        if (face[i].normal !== null && normals.length > 0) {
                            const normalIndex = face[i].normal;
                            normalsArray.push(
                                normals[normalIndex * 3],
                                normals[normalIndex * 3 + 1],
                                normals[normalIndex * 3 + 2]
                            );
                        }

                        if (face[i].uv !== null && uvs.length > 0) {
                            const uvIndex = face[i].uv;
                            uvsArray.push(
                                uvs[uvIndex * 2],
                                uvs[uvIndex * 2 + 1]
                            );
                        }
                    }

                    // 如果是四边形，添加第二个三角形
                    if (face.length === 4) {
                        const indices = [0, 2, 3];
                        for (let i of indices) {
                            const vertexIndex = face[i].vertex;
                            positions.push(
                                vertices[vertexIndex * 3],
                                vertices[vertexIndex * 3 + 1],
                                vertices[vertexIndex * 3 + 2]
                            );

                            if (face[i].normal !== null && normals.length > 0) {
                                const normalIndex = face[i].normal;
                                normalsArray.push(
                                    normals[normalIndex * 3],
                                    normals[normalIndex * 3 + 1],
                                    normals[normalIndex * 3 + 2]
                                );
                            }

                            if (face[i].uv !== null && uvs.length > 0) {
                                const uvIndex = face[i].uv;
                                uvsArray.push(
                                    uvs[uvIndex * 2],
                                    uvs[uvIndex * 2 + 1]
                                );
                            }
                        }
                    }
                }
            }

            geometry.setAttribute('position', new THREE.Float32BufferAttribute(positions, 3));

            if (normalsArray.length > 0) {
                geometry.setAttribute('normal', new THREE.Float32BufferAttribute(normalsArray, 3));
            } else {
                geometry.computeVertexNormals();
            }

            if (uvsArray.length > 0) {
                geometry.setAttribute('uv', new THREE.Float32BufferAttribute(uvsArray, 2));
            }

            return geometry;
        }
    }

    // 加载3D街道模型
    function load3DStreetModel() {
        return new Promise(async (resolve, reject) => {
            const objLoader = new SimpleOBJLoader();
            const textureLoader = new THREE.TextureLoader();

            try {
                const loadTexture = (path) => {
                    return new Promise((resolve, reject) => {
                        textureLoader.load(path, resolve, undefined, reject);
                    });
                };

                const diffuseTexture = await loadTexture(CONFIG.streetModel1.textures.diffuse);
                diffuseTexture.wrapS = THREE.RepeatWrapping;
                diffuseTexture.wrapT = THREE.RepeatWrapping;
                diffuseTexture.magFilter = THREE.LinearFilter;
                diffuseTexture.minFilter = THREE.LinearMipMapLinearFilter;

                const geometry = await objLoader.loadOBJ(CONFIG.streetModel1.objPath);
                const material = new THREE.MeshLambertMaterial({
                    map: diffuseTexture
                });

                const mesh = new THREE.Mesh(geometry, material);
                resolve(mesh);
            } catch (error) {
                reject(error);
            }
        });
    }

    // 加载3D城门模型
    function load3DCityGateModel() {
        return new Promise(async (resolve, reject) => {
            const objLoader = new SimpleOBJLoader();
            const textureLoader = new THREE.TextureLoader();

            try {
                const loadTexture = (path) => {
                    return new Promise((resolve, reject) => {
                        textureLoader.load(path, resolve, undefined, reject);
                    });
                };

                const diffuseTexture = await loadTexture(CONFIG.cityGateModel.textures.diffuse);
                diffuseTexture.wrapS = THREE.RepeatWrapping;
                diffuseTexture.wrapT = THREE.RepeatWrapping;
                diffuseTexture.magFilter = THREE.LinearFilter;
                diffuseTexture.minFilter = THREE.LinearMipMapLinearFilter;

                const geometry = await objLoader.loadOBJ(CONFIG.cityGateModel.objPath);
                const material = new THREE.MeshLambertMaterial({
                    map: diffuseTexture
                });

                const mesh = new THREE.Mesh(geometry, material);
                resolve(mesh);
            } catch (error) {
                reject(error);
            }
        });
    }

    // 创建道路
    function createRoad() {
        const roadGroup = new THREE.Group();
        const roadLength = 500;
        const roadWidth = CONFIG.roadWidth;
        const segmentCount = CONFIG.streetModel1.roadSegmentCount;
        const halfCount = Math.floor(segmentCount / 2);

        for (let i = -halfCount; i <= halfCount; i++) {
            const segmentGroup = new THREE.Group();
            segmentGroup.position.z = i * roadLength;

            // 道路主体
            const roadGeometry = new THREE.PlaneGeometry(roadWidth, roadLength);
            const roadTexture = new THREE.TextureLoader().load(CONFIG.textures.road1);
            roadTexture.wrapS = THREE.RepeatWrapping;
            roadTexture.wrapT = THREE.RepeatWrapping;
            roadTexture.repeat.set(1, 20);
            const roadMaterial = new THREE.MeshBasicMaterial({
                map: roadTexture
            });
            const roadMesh = new THREE.Mesh(roadGeometry, roadMaterial);
            roadMesh.rotation.x = -Math.PI / 2;
            roadMesh.position.y = 0;
            segmentGroup.add(roadMesh);

            // 道路边界线
            const lineGeometry = new THREE.BoxGeometry(0.1, 0.05, roadLength);
            const lineMaterial = new THREE.MeshLambertMaterial({ color: 0xc9b097 });

            const leftBorder = new THREE.Mesh(lineGeometry, lineMaterial);
            leftBorder.position.set(-roadWidth/2, 0.025, 0);
            segmentGroup.add(leftBorder);

            const rightBorder = new THREE.Mesh(lineGeometry, lineMaterial);
            rightBorder.position.set(roadWidth/2, 0.025, 0);
            segmentGroup.add(rightBorder);

            // 左右侧地面扩展区域
            const sideExtension = CONFIG.sideGroundExtension;
            const leftSideGeometry = new THREE.PlaneGeometry(sideExtension, roadLength);
            const leftSideTexture = new THREE.TextureLoader().load(CONFIG.textures.road2);
            leftSideTexture.wrapS = THREE.RepeatWrapping;
            leftSideTexture.wrapT = THREE.RepeatWrapping;
            const textureRepeatX = Math.max(1, Math.round(sideExtension / 3));
            leftSideTexture.repeat.set(textureRepeatX, 20);
            const leftSideMaterial = new THREE.MeshBasicMaterial({
                map: leftSideTexture
            });
            const leftSideMesh = new THREE.Mesh(leftSideGeometry, leftSideMaterial);
            leftSideMesh.rotation.x = -Math.PI / 2;
            leftSideMesh.position.set(-roadWidth/2 - sideExtension/2, 0, 0);
            segmentGroup.add(leftSideMesh);

            const rightSideGeometry = new THREE.PlaneGeometry(sideExtension, roadLength);
            const rightSideTexture = new THREE.TextureLoader().load(CONFIG.textures.road3);
            rightSideTexture.wrapS = THREE.RepeatWrapping;
            rightSideTexture.wrapT = THREE.RepeatWrapping;
            rightSideTexture.repeat.set(textureRepeatX, 20);
            const rightSideMaterial = new THREE.MeshBasicMaterial({
                map: rightSideTexture
            });
            const rightSideMesh = new THREE.Mesh(rightSideGeometry, rightSideMaterial);
            rightSideMesh.rotation.x = -Math.PI / 2;
            rightSideMesh.position.set(roadWidth/2 + sideExtension/2, 0, 0);
            segmentGroup.add(rightSideMesh);

            roadGroup.add(segmentGroup);
        }

        scene.add(roadGroup);
        road = roadGroup;
        markModelLoaded('road');
    }

    // 创建玩家
    function createPlayer() {
        const playerGroup = new THREE.Group();

        const loader = new THREE.TextureLoader();
        loader.load(CONFIG.textures.player, function(texture) {
            texture.wrapS = THREE.ClampToEdgeWrapping;
            texture.wrapT = THREE.ClampToEdgeWrapping;
            texture.magFilter = THREE.LinearFilter;
            texture.minFilter = THREE.LinearMipMapLinearFilter;

            // 镜像反转纹理
            texture.repeat.x = -1;
            texture.offset.x = 1;

            const img = texture.image;
            const aspect = img && img.height ? (img.width / img.height) : 1;
            const playerHeight = CONFIG.playerHeight;
            const playerWidth = playerHeight * aspect;

            const playerGeometry = new THREE.PlaneGeometry(playerWidth, playerHeight);
            const playerMaterial = new THREE.MeshBasicMaterial({
                map: texture,
                transparent: true,
                side: THREE.DoubleSide,
                opacity: 1
            });
            const playerMesh = new THREE.Mesh(playerGeometry, playerMaterial);
            playerMesh.position.y = playerHeight / 2;

            while(playerGroup.children.length > 0) {
                playerGroup.remove(playerGroup.children[0]);
            }

            playerGroup.add(playerMesh);
            window.playerMesh = playerMesh;
            markModelLoaded('player');

        }, undefined, function(error) {
            console.error('玩家图片加载失败:', error);
            markModelLoaded('player');
        });

        playerGroup.position.set(0, 0, playerZ);
        playerGroup.rotation.y = Math.PI;

        scene.add(playerGroup);
        player = playerGroup;
    }

    // 创建3D街道
    function create3DStreet(side, streetMesh) {
        const isLeft = side === 'left';
        const streetInstances = [];

        // 计算模型的边界框来确定间距和位置
        const box = new THREE.Box3().setFromObject(streetMesh);
        const modelSize = box.getSize(new THREE.Vector3());
        const spacing = Math.max(modelSize.z, 20);

        // 缩放调整
        const scale = CONFIG.streetHeight / Math.max(modelSize.y, 1);

        // 创建多个街道模型实例
        const modelCount = isLeft ? CONFIG.streetModel1.leftStreetCount : CONFIG.streetModel1.rightStreetCount;
        const frontCount = modelCount - 1;

        for (let i = 1; i >= -frontCount + 1; i--) {
            const streetClone = streetMesh.clone();
            streetClone.scale.set(scale, scale, scale);

            // 设置旋转
            if (isLeft) {
                streetClone.rotation.y = Math.PI / 2;
            } else {
                streetClone.rotation.y = -Math.PI / 2;
            }

            // 重新计算旋转后的边界框
            const rotatedBox = new THREE.Box3().setFromObject(streetClone);
            const rotatedSize = rotatedBox.getSize(new THREE.Vector3());

            // 位置设置
            let xPos;
            if (isLeft) {
                xPos = -3 - rotatedSize.x / 2;
            } else {
                xPos = 3 + rotatedSize.x / 2;
            }

            const yPos = 0;
            const zPos = i * spacing;

            streetClone.position.set(xPos, yPos, zPos);
            scene.add(streetClone);
            streetInstances.push(streetClone);
        }
    }

    // 创建3D城门
    function create3DCityGate(cityGateMesh) {
        // 计算模型的边界框来确定缩放
        const box = new THREE.Box3().setFromObject(cityGateMesh);
        const modelSize = box.getSize(new THREE.Vector3());

        // 缩放调整
        const scale = CONFIG.cityGateHeight / Math.max(modelSize.y, 1);
        cityGateMesh.scale.set(scale, scale, scale);

        // 位置设置
        const gateZ = -CONFIG.cityGateDistance;
        cityGateMesh.position.set(0, 0, gateZ);
        cityGateMesh.rotation.y = 0;

        // 添加到场景
        scene.add(cityGateMesh);
        window.cityGate = cityGateMesh;
    }

    // 加载资源
    const loadAssets = () => {
        return new Promise((resolve) => {
            console.log('开始加载资源...');

            // 存储原始的resolve函数，在checkAllModelsLoaded中调用
            window.loadAssetsResolve = resolve;

            // 开始加载所有模型
            createRoad();
            createPlayer();
            // 加载3D街道模型
            load3DStreetModel().then((streetModel) => {
                markModelLoaded('street');
                create3DStreet('left', streetModel);
                create3DStreet('right', streetModel);
            })
            // 加载3D城门模型
            load3DCityGateModel().then((cityGateModel) => {
                markModelLoaded('cityGate');
                create3DCityGate(cityGateModel);
            })
        });
    };

    // 限制玩家X位置在道路范围内
    function clampPlayerX(x) {
        const roadWidth = CONFIG.roadWidth;
        const maxX = roadWidth / 2 - 0.8;
        const minX = -maxX;
        return Math.max(minX, Math.min(maxX, x));
    }

    // 创建金币
    function createCoin(x, z) {
        const coinGroup = new THREE.Group();
        const coin = new THREE.Mesh(shared.coinGeometry, shared.coinMaterial);
        coinGroup.add(coin);
        coinGroup.position.set(x, 1, z);
        coinGroup.userData = { type: 'coin', x: x };
        scene.add(coinGroup);
        gameObjects.value.push(coinGroup);
        return coinGroup;
    }

    // 创建障碍物
    function createObstacle(x, z) {
        const obstacleGroup = new THREE.Group();
        const obstacle = new THREE.Mesh(shared.obstacleGeometry, shared.obstacleMaterial);
        obstacle.position.y = 0.75;
        obstacleGroup.add(obstacle);
        obstacleGroup.position.set(x, 0, z);
        obstacleGroup.userData = { type: 'obstacle', x: x };
        scene.add(obstacleGroup);
        gameObjects.value.push(obstacleGroup);
        return obstacleGroup;
    }

    // 生成游戏对象
    function spawnObjects() {
        const spawnZ = playerZ - 200;

        if (Math.abs(spawnZ - lastSpawnZ) >= 2) {
            lastSpawnZ = spawnZ;

            // 生成金币
            if (gameObjects.value.filter(o => o.userData.type === 'coin').length < MAX_COINS) {
                if (Math.random() < 0.15 && Math.abs(spawnZ - lastCoinZ) >= baseCoinGap) {
                    const roadWidth = CONFIG.roadWidth;
                    const x = (Math.random() - 0.5) * (roadWidth - 1);
                    createCoin(x, spawnZ);
                    lastCoinZ = spawnZ;
                }
            }

            // 生成障碍物
            if (gameObjects.value.filter(o => o.userData.type === 'obstacle').length < MAX_OBSTACLES) {
                if (Math.random() < obstacleSpawnBaseProb && Math.abs(spawnZ - lastObstacleZ) >= baseObstacleGap) {
                    const roadWidth = CONFIG.roadWidth;
                    const x = (Math.random() - 0.5) * (roadWidth - 1);
                    createObstacle(x, spawnZ);
                    lastObstacleZ = spawnZ;
                }
            }
        }
    }

    // 碰撞检测
    function checkCollisions() {
        for (let i = gameObjects.value.length - 1; i >= 0; i--) {
            const obj = gameObjects.value[i];
            const distance = Math.abs(obj.position.z - player.position.z);

            if (distance < 2) {
                if (obj.userData.type === 'coin') {
                    const xDistance = Math.abs(obj.position.x - player.position.x);
                    if (xDistance < 1.0) {
                        scene.remove(obj);
                        gameObjects.value.splice(i, 1);
                        score += 10;
                        gameState.value.coins = score;
                        const coinsEl = document.getElementById('coins');
                        if (coinsEl) coinsEl.textContent = score;
                    }
                } else if (obj.userData.type === 'obstacle') {
                    const xDistance = Math.abs(obj.position.x - player.position.x);
                    if (xDistance < 1.0) {
                        gameStateInternal = 'gameOver';
                        showGameOver();
                        return;
                    }
                }
            }
        }
    }

    // 更新摄像机位置
    function updateCamera() {
        const distance = CONFIG.cameraDistance;
        const height = CONFIG.cameraHeight;
        const lookAheadDistance = CONFIG.cameraLookAheadDistance;

        camera.position.set(0, player.position.y + height, player.position.z + distance);
        tempLookAt.set(0, player.position.y + 1, player.position.z - lookAheadDistance);
        camera.lookAt(tempLookAt);
    }

    // 固定步长的游戏逻辑更新
    function gameStep(dt) {
        if (gameStateInternal !== 'playing') return;

        // 玩家前进
        playerZ -= playerSpeed * dt;
        player.position.z = playerZ;
        player.position.x = playerX;

        // 更新距离
        distance = Math.floor(Math.abs(playerZ) / 2);
        gameState.value.distance = distance;
        const distanceEl = document.getElementById('distance');
        if (distanceEl) distanceEl.textContent = distance;

        // 生成对象
        spawnObjects();

        // 移除远离的对象
        for (let i = gameObjects.value.length - 1; i >= 0; i--) {
            const obj = gameObjects.value[i];
            if (obj.position.z > playerZ + 50) {
                scene.remove(obj);
                gameObjects.value.splice(i, 1);
                continue;
            }
            if (obj.position.z < playerZ - 400) {
                scene.remove(obj);
                gameObjects.value.splice(i, 1);
            }
        }

        // 更新跑道位置
        if (road) {
            road.position.z = Math.floor(playerZ / 500) * 500;
        }

        // 检查碰撞
        checkCollisions();

        // 更新摄像机
        updateCamera();
    }



    // 显示游戏结束
    function showGameOver() {
        gameState.value.showGameOver = true;
        gameState.value.finalScore = score;
        gameState.value.finalDistance = distance;

        const finalScoreEl = document.getElementById('finalScore');
        const finalDistanceEl = document.getElementById('finalDistance');
        const gameOverEl = document.getElementById('gameOver');

        if (finalScoreEl) finalScoreEl.textContent = score;
        if (finalDistanceEl) finalDistanceEl.textContent = distance;
        if (gameOverEl) gameOverEl.style.display = 'block';

        // 调用游戏结束回调
        if (onGameEnd) {
            onGameEnd(false); // 撞到障碍物算失败
        }
    }

    // 重新开始游戏
    function restartGame() {
        gameStateInternal = 'playing';
        score = 0;
        distance = 0;
        playerX = 0;
        playerZ = 0;
        lastObstacleZ = -Infinity;
        lastCoinZ = -Infinity;
        accumulator = 0;

        // 重置玩家位置
        player.position.set(0, 0, 0);

        // 清除所有游戏对象
        for (let obj of gameObjects.value) {
            scene.remove(obj);
        }
        gameObjects.value = [];
        for (let obj of sceneryObjects.value) {
            scene.remove(obj);
        }
        sceneryObjects.value = [];

        // 重置UI
        gameState.value.coins = 0;
        gameState.value.distance = 0;
        gameState.value.showGameOver = false;
        gameState.value.isRunning = true;
        gameState.value.gameStartTime = Date.now();

        const coinsEl = document.getElementById('coins');
        const distanceEl = document.getElementById('distance');
        const gameOverEl = document.getElementById('gameOver');
        if (coinsEl) coinsEl.textContent = '0';
        if (distanceEl) distanceEl.textContent = '0';
        if (gameOverEl) gameOverEl.style.display = 'none';

        clock.elapsedTime = 0;
        clock.start();
    }

    // 初始化游戏
    const initGame = async () => {
        if (!canvasContainerRef.value) return;

        try {
            initThreeJS();

            // 设置游戏状态为加载中
            gameStateInternal = 'loading';

            // 重置模型加载状态
            modelsLoaded = {
                street: false,
                cityGate: false,
                road: false,
                player: false
            };

            // 重置游戏状态
            gameState.value = {
                isRunning: false,
                isGameOver: false,
                coins: 0,
                distance: 0,
                fps: 0,
                showGameOver: false,
                finalScore: 0,
                finalDistance: 0,
                gameStartTime: 0
            };

            // 设置初始摄像机位置
            camera.position.set(0, CONFIG.cameraHeight, CONFIG.cameraDistance);
            camera.lookAt(0, 1, -CONFIG.cameraLookAheadDistance);

            // 开始渲染循环（显示加载状态）
            animate();

            console.log('初始化完成');
        } catch (error) {
            console.error('初始化失败:', error);
        }
    };

    // 渲染循环
    let animationFrame = null;
    function animate() {
        const delta = clock.getDelta();

        // 在loading状态显示加载信息
        if (gameStateInternal === 'loading') {
            const loadedCount = Object.values(modelsLoaded).filter(loaded => loaded).length;
            const totalCount = Object.keys(modelsLoaded).length;
            // 可以在这里显示加载进度，但不使用倒计时UI
            console.log(`加载进度: ${loadedCount}/${totalCount}`);
        }

        // 固定步长推进游戏逻辑
        if (gameStateInternal === 'playing') {
            accumulator += Math.min(delta, 0.1);
            while (accumulator >= FIXED_DT) {
                gameStep(FIXED_DT);
                accumulator -= FIXED_DT;
            }
        }

        // FPS 计算
        if (!window.__fpsSamples) {
            window.__fpsSamples = { sum: 0, count: 0, time: 0 };
        }
        const s = window.__fpsSamples;
        s.sum += (1 / Math.max(0.000001, delta));
        s.count += 1;
        s.time += delta;
        if (s.time >= 0.5) {
            const avgFps = Math.round(s.sum / s.count);
            gameState.value.fps = avgFps;
            const fpsEl = document.getElementById('fps');
            if (fpsEl) fpsEl.textContent = String(avgFps);
            s.sum = 0;
            s.count = 0;
            s.time = 0;
        }

        renderer.render(scene, camera);
        animationFrame = requestAnimationFrame(animate);
    }

    // 输入处理
    const handleKeyDown = (e) => {
        keys[e.key] = true;

        if (gameStateInternal === 'playing') {
            if (e.key === 'ArrowLeft' || e.key === 'a' || e.key === 'A') {
                playerX -= 0.2;
                playerX = clampPlayerX(playerX);
            }
            if (e.key === 'ArrowRight' || e.key === 'd' || e.key === 'D') {
                playerX += 0.2;
                playerX = clampPlayerX(playerX);
            }
        }
    };

    const handleKeyUp = (e) => {
        keys[e.key] = false;
    };

    // 触摸拖动控制
    let isDragging = false;
    let lastTouchX = 0;

    const handleTouchStart = (e) => {
        if (gameStateInternal === 'gameOver') {
            restartGame();
            return;
        }
        if (gameStateInternal === 'playing') {
            isDragging = true;
            lastTouchX = e.touches[0].clientX;
        }
    };

    const handleTouchMove = (e) => {
        if (gameStateInternal !== 'playing' || !isDragging) return;

        e.preventDefault();
        const currentTouchX = e.touches[0].clientX;
        const deltaX = currentTouchX - lastTouchX;

        const sensitivity = 0.01;
        playerX += deltaX * sensitivity;
        playerX = clampPlayerX(playerX);

        lastTouchX = currentTouchX;
    };

    const handleTouchEnd = () => {
        isDragging = false;
    };

    const handleClick = () => {
        if (gameStateInternal === 'gameOver') {
            restartGame();
        }
    };

    const handleResize = () => {
        camera.aspect = window.innerWidth / window.innerHeight;
        camera.updateProjectionMatrix();
        renderer.setSize(window.innerWidth, window.innerHeight);
        renderer.setPixelRatio(Math.min(window.devicePixelRatio || 1, 2));
    };

    // 开始游戏
    const startGame = () => {
        gameState.value.isRunning = true;
        gameState.value.isGameOver = false;
        gameState.value.gameStartTime = Date.now();
        gameStateInternal = 'playing';

        console.log('3D跑酷游戏开始');
    };

    // 添加事件监听
    const addEvents = () => {
        document.addEventListener('keydown', handleKeyDown);
        document.addEventListener('keyup', handleKeyUp);
        document.addEventListener('touchstart', handleTouchStart);
        document.addEventListener('touchmove', handleTouchMove);
        document.addEventListener('touchend', handleTouchEnd);
        document.addEventListener('click', handleClick);
        window.addEventListener('resize', handleResize);
    };

    // 移除事件监听
    const removeEvents = () => {
        document.removeEventListener('keydown', handleKeyDown);
        document.removeEventListener('keyup', handleKeyUp);
        document.removeEventListener('touchstart', handleTouchStart);
        document.removeEventListener('touchmove', handleTouchMove);
        document.removeEventListener('touchend', handleTouchEnd);
        document.removeEventListener('click', handleClick);
        window.removeEventListener('resize', handleResize);

        if (animationFrame) {
            cancelAnimationFrame(animationFrame);
        }
    };

    return {
        gameState,
        loadAssets,
        initGame,
        startGame,
        addEvents,
        removeEvents
    };
}
