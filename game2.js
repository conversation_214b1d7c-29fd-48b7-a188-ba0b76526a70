// 归途游戏
function useRunnerGame(canvasContainerRef, onGameEnd) {
    // 游戏配置
    const CONFIG = {
        // 纹理路径
        textures: {
            background: 'img/bj2.jpg',
            road1: 'img/wall1.jpg',//场景1地面图片
            road2: 'img/wall2.jpg',//场景2地面图片
            road3: 'img/wall3.jpg',//场景3地面图片
            player: 'img/player.png'
        },

        // 场景1街道模型
        streetModel1: {
            objPath: 'mould/street1/base.obj',
            textures: {
                diffuse: 'mould/street1/texture_diffuse.jpg',
            },
            leftStreetCount: 7,
            rightStreetCount: 7,
            roadSegmentCount: 5
        },
        // 场景2街道模型
        streetModel2: {
            objPath: 'mould/street2/base.obj',
            textures: {
                diffuse: 'mould/street2/texture_diffuse.jpg',
            },
            leftStreetCount: 7,
            rightStreetCount: 7,
            roadSegmentCount: 5
        },
        // 场景3街道模型
        streetModel3: {
            objPath: 'mould/street3/base.obj',
            textures: {
                diffuse: 'mould/street3/texture_diffuse.jpg',
            },
            leftStreetCount: 7,
            rightStreetCount: 7,
            roadSegmentCount: 5
        },

        // 3D城门模型配置
        cityGateModel: {
            objPath: 'mould/door/base.obj',
            textures: {
                diffuse: 'mould/door/texture_diffuse.jpg',
            }
        },

        // 高度配置
        cityGateHeight: 30,
        streetHeight: 18,
        playerHeight: 2,

        // 位置配置
        cityGateDistance: 100,
        roadWidth: 6,
        sideGroundExtension: 50,

        // 摄像机配置
        cameraDistance: 2,
        cameraHeight: 4.3,
        cameraAngle: 120,
        cameraLookAheadDistance: 100
    };

    // 游戏状态
    const gameState = ref({
        isRunning: false,
        isGameOver: false,
        distance: 0,
        fps: 0,
        showGameOver: false,
        finalDistance: 0,
        gameStartTime: 0,
        countdown: 3,
        gameTime: 60,
        currentScene: 1,
        npcMet: [false, false, false],
        gamePhase: 'loading' // loading, countdown, playing, paused, gameOver
    });

    // 游戏变量
    let scene, camera, renderer;
    let player, road;
    const gameObjects = ref([]);
    const sceneryObjects = ref([]);
    const npcs = ref([]);
    let gameStateInternal = 'loading'; // loading, countdown, playing, paused, gameOver
    let playerSpeed = 4;
    let basePlayerSpeed = 4;
    let distance = 0;
    let keys = {};
    let clock = new THREE.Clock();
    let accumulator = 0;
    const FIXED_DT = 1 / 60;
    let countdownTimer = null;
    let gameTimer = null;

    // 模型加载状态跟踪
    let modelsLoaded = {
        street1: false,
        street2: false,
        street3: false,
        cityGate: false,
        road: false,
        player: false
    };

    // 玩家状态
    let playerX = 0;
    let playerZ = 0;

    // NPC相关变量
    let currentNPCIndex = -1;
    let isDialogActive = false;

    // 共享几何与材质
    const shared = {
        npcGeometry: new THREE.PlaneGeometry(2, 2),
        dialogGeometry: new THREE.PlaneGeometry(3, 2)
    };
    const tempLookAt = new THREE.Vector3();

    // 检查所有模型是否加载完成
    function checkAllModelsLoaded() {
        const allLoaded = Object.values(modelsLoaded).every(loaded => loaded);
        console.log('模型加载状态:', modelsLoaded);
        if (allLoaded && gameStateInternal === 'loading') {
            console.log('所有模型加载完成，开始倒计时');

            // 调用loadAssets的resolve
            if (window.loadAssetsResolve) {
                window.loadAssetsResolve();
                window.loadAssetsResolve = null;
            }

            // 确保gameState存在并且有正确的结构
            if (!gameState.value) {
                console.error('gameState.value 不存在');
                return;
            }

            // 开始倒计时
            startCountdown();
        }
    }

    // 标记模型加载完成
    function markModelLoaded(modelName) {
        modelsLoaded[modelName] = true;
        checkAllModelsLoaded();
    }

    // 初始化Three.js场景
    function initThreeJS() {
        // 创建场景
        scene = new THREE.Scene();

        // 创建摄像机
        camera = new THREE.PerspectiveCamera(CONFIG.cameraAngle, window.innerWidth / window.innerHeight, 0.1, 1000);

        // 创建渲染器
        renderer = new THREE.WebGLRenderer({ antialias: true, alpha: true });
        renderer.setSize(window.innerWidth, window.innerHeight);
        renderer.setClearColor(0x000000, 0);
        renderer.setPixelRatio(Math.min(window.devicePixelRatio || 1, 2));
        renderer.shadowMap.enabled = false;

        // 将渲染器添加到指定的容器
        if (canvasContainerRef.value) {
            canvasContainerRef.value.appendChild(renderer.domElement);
        }

        // 添加光源
        const ambientLight = new THREE.AmbientLight(0xffffff, 0.6);
        scene.add(ambientLight);

        const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
        directionalLight.position.set(10, 20, 5);
        scene.add(directionalLight);
    }

    // 简化的OBJ加载器
    class SimpleOBJLoader {
        constructor() {
            this.textureLoader = new THREE.TextureLoader();
        }

        async loadOBJ(objPath) {
            try {
                const response = await fetch(objPath);
                const objText = await response.text();
                return this.parseOBJ(objText);
            } catch (error) {
                throw new Error(`Failed to load OBJ file: ${error.message}`);
            }
        }

        parseOBJ(objText) {
            const vertices = [];
            const normals = [];
            const uvs = [];
            const faces = [];

            const lines = objText.split('\n');

            for (let line of lines) {
                line = line.trim();
                if (line.startsWith('v ')) {
                    const parts = line.split(/\s+/);
                    vertices.push(
                        parseFloat(parts[1]),
                        parseFloat(parts[2]),
                        parseFloat(parts[3])
                    );
                } else if (line.startsWith('vn ')) {
                    const parts = line.split(/\s+/);
                    normals.push(
                        parseFloat(parts[1]),
                        parseFloat(parts[2]),
                        parseFloat(parts[3])
                    );
                } else if (line.startsWith('vt ')) {
                    const parts = line.split(/\s+/);
                    uvs.push(
                        parseFloat(parts[1]),
                        parseFloat(parts[2])
                    );
                } else if (line.startsWith('f ')) {
                    const parts = line.split(/\s+/).slice(1);
                    const face = [];
                    for (let part of parts) {
                        const indices = part.split('/');
                        face.push({
                            vertex: parseInt(indices[0]) - 1,
                            uv: indices[1] ? parseInt(indices[1]) - 1 : null,
                            normal: indices[2] ? parseInt(indices[2]) - 1 : null
                        });
                    }
                    faces.push(face);
                }
            }

            // 创建Three.js几何体
            const geometry = new THREE.BufferGeometry();
            const positions = [];
            const normalsArray = [];
            const uvsArray = [];

            for (let face of faces) {
                if (face.length >= 3) {
                    // 处理三角形
                    for (let i = 0; i < 3; i++) {
                        const vertexIndex = face[i].vertex;
                        positions.push(
                            vertices[vertexIndex * 3],
                            vertices[vertexIndex * 3 + 1],
                            vertices[vertexIndex * 3 + 2]
                        );

                        if (face[i].normal !== null && normals.length > 0) {
                            const normalIndex = face[i].normal;
                            normalsArray.push(
                                normals[normalIndex * 3],
                                normals[normalIndex * 3 + 1],
                                normals[normalIndex * 3 + 2]
                            );
                        }

                        if (face[i].uv !== null && uvs.length > 0) {
                            const uvIndex = face[i].uv;
                            uvsArray.push(
                                uvs[uvIndex * 2],
                                uvs[uvIndex * 2 + 1]
                            );
                        }
                    }

                    // 如果是四边形，添加第二个三角形
                    if (face.length === 4) {
                        const indices = [0, 2, 3];
                        for (let i of indices) {
                            const vertexIndex = face[i].vertex;
                            positions.push(
                                vertices[vertexIndex * 3],
                                vertices[vertexIndex * 3 + 1],
                                vertices[vertexIndex * 3 + 2]
                            );

                            if (face[i].normal !== null && normals.length > 0) {
                                const normalIndex = face[i].normal;
                                normalsArray.push(
                                    normals[normalIndex * 3],
                                    normals[normalIndex * 3 + 1],
                                    normals[normalIndex * 3 + 2]
                                );
                            }

                            if (face[i].uv !== null && uvs.length > 0) {
                                const uvIndex = face[i].uv;
                                uvsArray.push(
                                    uvs[uvIndex * 2],
                                    uvs[uvIndex * 2 + 1]
                                );
                            }
                        }
                    }
                }
            }

            geometry.setAttribute('position', new THREE.Float32BufferAttribute(positions, 3));

            if (normalsArray.length > 0) {
                geometry.setAttribute('normal', new THREE.Float32BufferAttribute(normalsArray, 3));
            } else {
                geometry.computeVertexNormals();
            }

            if (uvsArray.length > 0) {
                geometry.setAttribute('uv', new THREE.Float32BufferAttribute(uvsArray, 2));
            }

            return geometry;
        }
    }

    // 加载3D街道模型
    function load3DStreetModel(sceneNumber) {
        return new Promise(async (resolve, reject) => {
            const objLoader = new SimpleOBJLoader();
            const textureLoader = new THREE.TextureLoader();

            try {
                const loadTexture = (path) => {
                    return new Promise((resolve, reject) => {
                        textureLoader.load(path, resolve, undefined, reject);
                    });
                };

                const streetConfig = CONFIG[`streetModel${sceneNumber}`];
                const diffuseTexture = await loadTexture(streetConfig.textures.diffuse);
                diffuseTexture.wrapS = THREE.RepeatWrapping;
                diffuseTexture.wrapT = THREE.RepeatWrapping;
                diffuseTexture.magFilter = THREE.LinearFilter;
                diffuseTexture.minFilter = THREE.LinearMipMapLinearFilter;

                const geometry = await objLoader.loadOBJ(streetConfig.objPath);
                const material = new THREE.MeshLambertMaterial({
                    map: diffuseTexture
                });

                const mesh = new THREE.Mesh(geometry, material);
                resolve(mesh);
            } catch (error) {
                reject(error);
            }
        });
    }

    // 加载3D城门模型
    function load3DCityGateModel() {
        return new Promise(async (resolve, reject) => {
            const objLoader = new SimpleOBJLoader();
            const textureLoader = new THREE.TextureLoader();

            try {
                const loadTexture = (path) => {
                    return new Promise((resolve, reject) => {
                        textureLoader.load(path, resolve, undefined, reject);
                    });
                };

                const diffuseTexture = await loadTexture(CONFIG.cityGateModel.textures.diffuse);
                diffuseTexture.wrapS = THREE.RepeatWrapping;
                diffuseTexture.wrapT = THREE.RepeatWrapping;
                diffuseTexture.magFilter = THREE.LinearFilter;
                diffuseTexture.minFilter = THREE.LinearMipMapLinearFilter;

                const geometry = await objLoader.loadOBJ(CONFIG.cityGateModel.objPath);
                const material = new THREE.MeshLambertMaterial({
                    map: diffuseTexture
                });

                const mesh = new THREE.Mesh(geometry, material);
                resolve(mesh);
            } catch (error) {
                reject(error);
            }
        });
    }

    // 创建道路
    function createRoad() {
        const roadGroup = new THREE.Group();
        const roadLength = 300; // 每个场景的道路长度
        const roadWidth = CONFIG.roadWidth;

        // 场景1道路 (0 到 -300)
        createRoadSegment(roadGroup, 0, roadLength, roadWidth, 1, 0);

        // 城门位置 (-300 到 -400)
        createRoadSegment(roadGroup, 1, 100, roadWidth, 1, -300);

        // 场景2道路 (-400 到 -700)
        createRoadSegment(roadGroup, 2, roadLength, roadWidth, 2, -400);

        // 场景3道路 (-700 到 -1000)
        createRoadSegment(roadGroup, 3, roadLength, roadWidth, 3, -700);

        scene.add(roadGroup);
        road = roadGroup;
        console.log('道路创建完成，包含4个道路段');
        markModelLoaded('road');
    }

    // 创建单个道路段
    function createRoadSegment(roadGroup, segmentIndex, roadLength, roadWidth, sceneNumber, zPosition) {
        const segmentGroup = new THREE.Group();
        segmentGroup.position.z = zPosition;

        // 根据场景选择纹理
        const roadTexture = CONFIG.textures[`road${sceneNumber}`];
        console.log(`创建道路段 ${segmentIndex}，位置 z=${zPosition}，场景${sceneNumber}，纹理：${roadTexture}`);

        // 道路主体
        const roadGeometry = new THREE.PlaneGeometry(roadWidth, roadLength);
        const roadTextureLoader = new THREE.TextureLoader().load(roadTexture);
        roadTextureLoader.wrapS = THREE.RepeatWrapping;
        roadTextureLoader.wrapT = THREE.RepeatWrapping;
        roadTextureLoader.repeat.set(1, 10);
        const roadMaterial = new THREE.MeshBasicMaterial({
            map: roadTextureLoader
        });
        const roadMesh = new THREE.Mesh(roadGeometry, roadMaterial);
        roadMesh.rotation.x = -Math.PI / 2;
        roadMesh.position.y = 0;
        segmentGroup.add(roadMesh);

        // 道路边界线
        const lineGeometry = new THREE.BoxGeometry(0.1, 0.05, roadLength);
        const lineMaterial = new THREE.MeshLambertMaterial({ color: 0xc9b097 });

        const leftBorder = new THREE.Mesh(lineGeometry, lineMaterial);
        leftBorder.position.set(-roadWidth/2, 0.025, 0);
        segmentGroup.add(leftBorder);

        const rightBorder = new THREE.Mesh(lineGeometry, lineMaterial);
        rightBorder.position.set(roadWidth/2, 0.025, 0);
        segmentGroup.add(rightBorder);

        // 左右侧地面扩展区域
        const sideExtension = CONFIG.sideGroundExtension;
        const leftSideGeometry = new THREE.PlaneGeometry(sideExtension, roadLength);
        const leftSideTexture = new THREE.TextureLoader().load(roadTexture);
        leftSideTexture.wrapS = THREE.RepeatWrapping;
        leftSideTexture.wrapT = THREE.RepeatWrapping;
        const textureRepeatX = Math.max(1, Math.round(sideExtension / 3));
        leftSideTexture.repeat.set(textureRepeatX, 10);
        const leftSideMaterial = new THREE.MeshBasicMaterial({
            map: leftSideTexture
        });
        const leftSideMesh = new THREE.Mesh(leftSideGeometry, leftSideMaterial);
        leftSideMesh.rotation.x = -Math.PI / 2;
        leftSideMesh.position.set(-roadWidth/2 - sideExtension/2, 0, 0);
        segmentGroup.add(leftSideMesh);

        const rightSideGeometry = new THREE.PlaneGeometry(sideExtension, roadLength);
        const rightSideTexture = new THREE.TextureLoader().load(roadTexture);
        rightSideTexture.wrapS = THREE.RepeatWrapping;
        rightSideTexture.wrapT = THREE.RepeatWrapping;
        rightSideTexture.repeat.set(textureRepeatX, 10);
        const rightSideMaterial = new THREE.MeshBasicMaterial({
            map: rightSideTexture
        });
        const rightSideMesh = new THREE.Mesh(rightSideGeometry, rightSideMaterial);
        rightSideMesh.rotation.x = -Math.PI / 2;
        rightSideMesh.position.set(roadWidth/2 + sideExtension/2, 0, 0);
        segmentGroup.add(rightSideMesh);

        roadGroup.add(segmentGroup);
    }

    // 创建玩家
    function createPlayer() {
        const playerGroup = new THREE.Group();

        const loader = new THREE.TextureLoader();
        loader.load(CONFIG.textures.player, function(texture) {
            texture.wrapS = THREE.ClampToEdgeWrapping;
            texture.wrapT = THREE.ClampToEdgeWrapping;
            texture.magFilter = THREE.LinearFilter;
            texture.minFilter = THREE.LinearMipMapLinearFilter;

            // 镜像反转纹理
            texture.repeat.x = -1;
            texture.offset.x = 1;

            const img = texture.image;
            const aspect = img && img.height ? (img.width / img.height) : 1;
            const playerHeight = CONFIG.playerHeight;
            const playerWidth = playerHeight * aspect;

            const playerGeometry = new THREE.PlaneGeometry(playerWidth, playerHeight);
            const playerMaterial = new THREE.MeshBasicMaterial({
                map: texture,
                transparent: true,
                side: THREE.DoubleSide,
                opacity: 1
            });
            const playerMesh = new THREE.Mesh(playerGeometry, playerMaterial);
            playerMesh.position.y = playerHeight / 2;

            while(playerGroup.children.length > 0) {
                playerGroup.remove(playerGroup.children[0]);
            }

            playerGroup.add(playerMesh);
            window.playerMesh = playerMesh;
            markModelLoaded('player');

        }, undefined, function(error) {
            console.error('玩家图片加载失败:', error);
            markModelLoaded('player');
        });

        playerGroup.position.set(0, 0, playerZ);
        playerGroup.rotation.y = Math.PI;

        scene.add(playerGroup);
        player = playerGroup;
    }

    // 创建3D街道
    function create3DStreet(side, streetMesh, positions) {
        const isLeft = side === 'left';

        if (!streetMesh) {
            console.warn('街道模型未找到');
            return;
        }

        // 计算模型的边界框来确定缩放
        const box = new THREE.Box3().setFromObject(streetMesh);
        const modelSize = box.getSize(new THREE.Vector3());

        // 缩放调整
        const scale = CONFIG.streetHeight / Math.max(modelSize.y, 1);

        positions.forEach((pos, index) => {
            const streetClone = streetMesh.clone();
            streetClone.scale.set(scale, scale, scale);

            // 设置旋转
            if (isLeft) {
                streetClone.rotation.y = Math.PI / 2;
            } else {
                streetClone.rotation.y = -Math.PI / 2;
            }

            // 重新计算旋转后的边界框
            const rotatedBox = new THREE.Box3().setFromObject(streetClone);
            const rotatedSize = rotatedBox.getSize(new THREE.Vector3());

            // 位置设置
            let xPos;
            if (isLeft) {
                xPos = -5 - rotatedSize.x / 2;
            } else {
                xPos = 5 + rotatedSize.x / 2;
            }

            streetClone.position.set(xPos, 0, pos);
            scene.add(streetClone);
            console.log(`创建${isLeft ? '左' : '右'}侧街道模型，位置: x=${xPos}, z=${pos}`);
        });
    }

    // 创建3D城门
    function create3DCityGate(cityGateMesh) {
        // 计算模型的边界框来确定缩放
        const box = new THREE.Box3().setFromObject(cityGateMesh);
        const modelSize = box.getSize(new THREE.Vector3());

        // 缩放调整
        const scale = CONFIG.cityGateHeight / Math.max(modelSize.y, 1);
        cityGateMesh.scale.set(scale, scale, scale);

        // 位置设置 - 城门在场景1和场景2之间 (-300位置)
        const gateZ = -350; // 在场景1结束和场景2开始之间
        cityGateMesh.position.set(0, 0, gateZ);
        cityGateMesh.rotation.y = 0;

        // 添加到场景
        scene.add(cityGateMesh);
        console.log(`城门创建完成，位置: z=${gateZ}`);
        window.cityGate = cityGateMesh;
    }

    // 加载资源
    const loadAssets = () => {
        return new Promise((resolve) => {
            console.log('开始加载资源...');

            // 存储原始的resolve函数，在checkAllModelsLoaded中调用
            window.loadAssetsResolve = resolve;

            // 开始加载所有模型
            createRoad();
            createPlayer();

            // 加载场景1街道模型 - 3个模型
            load3DStreetModel(1).then((streetModel1) => {
                // 3个场景1街道模型的位置
                const scene1Positions = [-100, -150, -200];
                create3DStreet('left', streetModel1, scene1Positions);
                create3DStreet('right', streetModel1, scene1Positions);
                markModelLoaded('street1');
            }).catch(error => {
                console.error('场景1街道模型加载失败:', error);
                markModelLoaded('street1');
            });

            // 加载场景2街道模型 - 3个模型
            load3DStreetModel(2).then((streetModel2) => {
                // 3个场景2街道模型的位置 (城门后)
                const scene2Positions = [-450, -500, -550];
                create3DStreet('left', streetModel2, scene2Positions);
                create3DStreet('right', streetModel2, scene2Positions);
                markModelLoaded('street2');
            }).catch(error => {
                console.error('场景2街道模型加载失败:', error);
                markModelLoaded('street2');
            });

            // 加载场景3街道模型 - 3个模型
            load3DStreetModel(3).then((streetModel3) => {
                // 3个场景3街道模型的位置
                const scene3Positions = [-750, -800, -850];
                create3DStreet('left', streetModel3, scene3Positions);
                create3DStreet('right', streetModel3, scene3Positions);
                markModelLoaded('street3');
            }).catch(error => {
                console.error('场景3街道模型加载失败:', error);
                markModelLoaded('street3');
            });

            // 加载3D城门模型
            load3DCityGateModel().then((cityGateModel) => {
                markModelLoaded('cityGate');
                create3DCityGate(cityGateModel);
            }).catch(error => {
                console.error('城门模型加载失败:', error);
                markModelLoaded('cityGate');
            });
        });
    };

    // 限制玩家X位置在道路范围内
    function clampPlayerX(x) {
        const roadWidth = CONFIG.roadWidth;
        const maxX = roadWidth / 2 - 0.8;
        const minX = -maxX;
        return Math.max(minX, Math.min(maxX, x));
    }

    // 创建NPC
    function createNPC(npcData, x, z) {
        const npcGroup = new THREE.Group();

        // 加载NPC图片
        const loader = new THREE.TextureLoader();
        loader.load(npcData.people, function(texture) {
            texture.wrapS = THREE.ClampToEdgeWrapping;
            texture.wrapT = THREE.ClampToEdgeWrapping;

            const npcMaterial = new THREE.MeshBasicMaterial({
                map: texture,
                transparent: true,
                side: THREE.DoubleSide
            });
            const npcMesh = new THREE.Mesh(shared.npcGeometry, npcMaterial);
            npcMesh.position.y = 1;
            npcGroup.add(npcMesh);
        });

        npcGroup.position.set(x, 0, z);
        npcGroup.userData = {
            type: 'npc',
            npcData: npcData,
            hasDialog: false,
            dialogMesh: null
        };
        scene.add(npcGroup);
        npcs.value.push(npcGroup);
        return npcGroup;
    }

    // 显示NPC对话气泡
    function showNPCDialog(npcGroup) {
        if (npcGroup.userData.hasDialog) return;

        const npcData = npcGroup.userData.npcData;
        const loader = new THREE.TextureLoader();

        loader.load(npcData.dialog, function(texture) {
            texture.wrapS = THREE.ClampToEdgeWrapping;
            texture.wrapT = THREE.ClampToEdgeWrapping;

            const dialogMaterial = new THREE.MeshBasicMaterial({
                map: texture,
                transparent: true,
                side: THREE.DoubleSide
            });
            const dialogMesh = new THREE.Mesh(shared.dialogGeometry, dialogMaterial);
            dialogMesh.position.set(0, 3, 0);
            npcGroup.add(dialogMesh);

            npcGroup.userData.hasDialog = true;
            npcGroup.userData.dialogMesh = dialogMesh;
        });
    }

    // 初始化NPCs
    function initNPCs() {
        // 从HTML中获取question数据
        const questionData = window.question || [];

        // 在不同场景位置创建NPCs
        const npcPositions = [
            { x: 0, z: -150 }, // 场景1 NPC
            { x: 0, z: -500 }, // 场景2 NPC (城门后)
            { x: 0, z: -800 }  // 场景3 NPC
        ];

        questionData.forEach((npcData, index) => {
            if (index < npcPositions.length) {
                const pos = npcPositions[index];
                createNPC(npcData, pos.x, pos.z);
                console.log(`创建NPC ${index + 1}，位置: z=${pos.z}`);
            }
        });
    }

    // 检查NPC交互
    function checkNPCInteraction() {
        if (isDialogActive) return;

        // 确保npcMet数组存在
        if (!gameState.value.npcMet) {
            gameState.value.npcMet = [false, false, false];
        }

        npcs.value.forEach((npc, index) => {
            if (!npc || !player) return;

            const distance = Math.abs(npc.position.z - player.position.z);
            const xDistance = Math.abs(npc.position.x - player.position.x);

            if (distance < 10 && xDistance < 3 && !gameState.value.npcMet[index]) {
                // 显示NPC对话气泡
                showNPCDialog(npc);

                // 暂停游戏并显示选择界面
                gameStateInternal = 'paused';
                gameState.value.gamePhase = 'paused';
                isDialogActive = true;
                currentNPCIndex = index;

                // 显示选择界面
                showDialogChoice(npc.userData.npcData, index);

                // 标记该NPC已遇到
                gameState.value.npcMet[index] = true;
            }
        });
    }

    // 显示对话选择界面
    function showDialogChoice(npcData, npcIndex) {
        // 创建选择界面HTML
        const gameArea = document.querySelector('.game_area');
        if (!gameArea) {
            console.error('找不到 .game_area 元素');
            return;
        }

        // 移除已存在的对话框
        const existingDialog = document.getElementById('dialogChoice');
        if (existingDialog) {
            existingDialog.remove();
        }

        const dialogHTML = `
            <div class="dialog-choice" id="dialogChoice">
                <div class="dialog-box">
                    <img src="img/selectbox.png" class="select-box-bg">
                    <div class="dialog-content">
                        <div class="npc-info">
                            <img src="${npcData.people}" class="npc-avatar">
                            <div class="npc-desc">${npcData.desc}</div>
                        </div>
                        <div class="dialog-title">${npcData.title}</div>
                        <div class="dialog-options">
                            ${npcData.option.map((opt, index) =>
                                `<button class="dialog-option" data-speed="${opt.speed}" data-text="${opt.text}" onclick="selectOption(${index}, ${npcIndex})">${opt.text}</button>`
                            ).join('')}
                        </div>
                    </div>
                </div>
            </div>
        `;

        try {
            gameArea.insertAdjacentHTML('beforeend', dialogHTML);
        } catch (error) {
            console.error('插入对话框HTML失败:', error);
        }
    }

    // 选择回答选项
    window.selectOption = function(optionIndex, npcIndex) {
        const questionData = window.question || [];
        const selectedOption = questionData[npcIndex].option[optionIndex];

        // 应用速度效果
        applySpeedEffect(selectedOption.speed);

        // 显示玩家回答气泡
        showPlayerResponse(selectedOption.text);

        // 移除选择界面
        const dialogChoice = document.getElementById('dialogChoice');
        if (dialogChoice) {
            dialogChoice.remove();
        }

        // 恢复游戏
        setTimeout(() => {
            isDialogActive = false;
            gameStateInternal = 'playing';
            gameState.value.gamePhase = 'playing';
        }, 2000); // 显示回答2秒后继续游戏
    };

    // 应用速度效果
    function applySpeedEffect(speedEffect) {
        if (speedEffect === 1) {
            // 加速
            playerSpeed = basePlayerSpeed * 1.5;
        } else if (speedEffect === -1) {
            // 减速
            playerSpeed = basePlayerSpeed * 0.5;
        } else {
            // 无变化
            playerSpeed = basePlayerSpeed;
        }

        // 3秒后恢复正常速度
        setTimeout(() => {
            playerSpeed = basePlayerSpeed;
        }, 3000);
    }

    // 显示玩家回答气泡
    function showPlayerResponse(responseText) {
        const loader = new THREE.TextureLoader();
        loader.load('img/dialog2.png', function(texture) {
            texture.wrapS = THREE.ClampToEdgeWrapping;
            texture.wrapT = THREE.ClampToEdgeWrapping;

            const dialogMaterial = new THREE.MeshBasicMaterial({
                map: texture,
                transparent: true,
                side: THREE.DoubleSide
            });
            const dialogMesh = new THREE.Mesh(shared.dialogGeometry, dialogMaterial);
            dialogMesh.position.set(0, 3, 0);
            player.add(dialogMesh);

            // 2秒后移除气泡
            setTimeout(() => {
                player.remove(dialogMesh);
            }, 2000);
        });
    }

    // 更新摄像机位置
    function updateCamera() {
        const distance = CONFIG.cameraDistance;
        const height = CONFIG.cameraHeight;
        const lookAheadDistance = CONFIG.cameraLookAheadDistance;

        camera.position.set(0, player.position.y + height, player.position.z + distance);
        tempLookAt.set(0, player.position.y + 1, player.position.z - lookAheadDistance);
        camera.lookAt(tempLookAt);
    }

    // 开始倒计时
    function startCountdown() {
        // 使用setTimeout确保Vue组件已经更新
        setTimeout(() => {
            gameStateInternal = 'countdown';
            gameState.value.gamePhase = 'countdown';
            gameState.value.countdown = 3;

            countdownTimer = setInterval(() => {
                gameState.value.countdown--;

                if (gameState.value.countdown <= 0) {
                    clearInterval(countdownTimer);
                    startGameplay();
                }
            }, 1000);
        }, 100);
    }

    // 开始游戏
    function startGameplay() {
        gameStateInternal = 'playing';
        gameState.value.gamePhase = 'playing';
        gameState.value.gameTime = 60;

        // 初始化速度
        basePlayerSpeed = 1;
        playerSpeed = basePlayerSpeed;

        // 初始化NPCs
        initNPCs();

        // 开始游戏计时
        gameTimer = setInterval(() => {
            gameState.value.gameTime--;

            if (gameState.value.gameTime <= 0) {
                clearInterval(gameTimer);
                endGame(false); // 时间到，游戏失败
            }
        }, 1000);
    }

    // 固定步长的游戏逻辑更新
    function gameStep(dt) {
        if (gameStateInternal !== 'playing') return;

        // 玩家前进
        playerZ -= playerSpeed * dt;
        player.position.z = playerZ;
        player.position.x = playerX;

        // 更新距离
        distance = Math.floor(Math.abs(playerZ) / 2);
        gameState.value.distance = distance;
        const distanceEl = document.getElementById('distance');
        if (distanceEl) distanceEl.textContent = distance;

        // 检查NPC交互
        checkNPCInteraction();

        // 道路和街道模型都是静态的，不需要动态移动

        // 检查是否通过所有场景
        checkGameProgress();

        // 更新摄像机
        updateCamera();
    }

    // 检查游戏进度
    function checkGameProgress() {
        // 确保npcMet数组存在
        if (!gameState.value.npcMet) {
            gameState.value.npcMet = [false, false, false];
        }

        // 检查是否遇到了所有3个NPC
        const allNPCsMet = gameState.value.npcMet.every(met => met);

        // 更新当前场景
        if (playerZ > -300) {
            gameState.value.currentScene = 1;
        } else if (playerZ > -700) {
            gameState.value.currentScene = 2;
        } else {
            gameState.value.currentScene = 3;
        }

        // 如果遇到了所有NPC并且到达了终点
        if (allNPCsMet && playerZ <= -1000) {
            clearInterval(gameTimer);
            endGame(true); // 游戏成功
        }
    }

    // 结束游戏
    function endGame(success) {
        gameStateInternal = 'gameOver';
        gameState.value.gamePhase = 'gameOver';
        gameState.value.showGameOver = true;
        gameState.value.finalDistance = distance;

        // 调用游戏结束回调
        if (onGameEnd) {
            onGameEnd(success);
        }
    }



    // 重新开始游戏
    function restartGame() {
        // 清除计时器
        if (countdownTimer) clearInterval(countdownTimer);
        if (gameTimer) clearInterval(gameTimer);

        // 重置游戏状态
        gameStateInternal = 'countdown';
        distance = 0;
        playerX = 0;
        playerZ = 0;
        playerSpeed = basePlayerSpeed;
        accumulator = 0;
        isDialogActive = false;
        currentNPCIndex = -1;

        // 重置玩家位置
        player.position.set(0, 0, 0);

        // 清除所有游戏对象和NPCs
        for (let obj of gameObjects.value) {
            scene.remove(obj);
        }
        gameObjects.value = [];

        for (let npc of npcs.value) {
            scene.remove(npc);
        }
        npcs.value = [];

        for (let obj of sceneryObjects.value) {
            scene.remove(obj);
        }
        sceneryObjects.value = [];

        // 重置UI状态
        gameState.value.distance = 0;
        gameState.value.showGameOver = false;
        gameState.value.isRunning = true;
        gameState.value.gameStartTime = Date.now();
        gameState.value.countdown = 3;
        gameState.value.gameTime = 60;
        gameState.value.currentScene = 1;
        gameState.value.npcMet = [false, false, false];
        gameState.value.gamePhase = 'countdown';

        // 移除可能存在的对话框
        const dialogChoice = document.getElementById('dialogChoice');
        if (dialogChoice) {
            dialogChoice.remove();
        }

        const distanceEl = document.getElementById('distance');
        if (distanceEl) distanceEl.textContent = '0';

        clock.elapsedTime = 0;
        clock.start();

        // 开始新的倒计时
        startCountdown();
    }

    // 初始化游戏
    const initGame = async () => {
        if (!canvasContainerRef.value) return;

        try {
            initThreeJS();

            // 设置游戏状态为加载中
            gameStateInternal = 'loading';

            // 重置模型加载状态
            modelsLoaded = {
                street1: false,
                street2: false,
                street3: false,
                cityGate: false,
                road: false,
                player: false
            };

            // 重置游戏状态
            gameState.value = {
                isRunning: false,
                isGameOver: false,
                distance: 0,
                fps: 0,
                showGameOver: false,
                finalDistance: 0,
                gameStartTime: 0,
                countdown: 3,
                gameTime: 60,
                currentScene: 1,
                npcMet: [false, false, false],
                gamePhase: 'loading'
            };

            // 设置初始摄像机位置
            camera.position.set(0, CONFIG.cameraHeight, CONFIG.cameraDistance);
            camera.lookAt(0, 1, -CONFIG.cameraLookAheadDistance);

            // 开始渲染循环（显示加载状态）
            animate();

            console.log('初始化完成');
        } catch (error) {
            console.error('初始化失败:', error);
        }
    };

    // 渲染循环
    let animationFrame = null;
    function animate() {
        const delta = clock.getDelta();

        // 在loading状态显示加载信息
        if (gameStateInternal === 'loading') {
            const loadedCount = Object.values(modelsLoaded).filter(loaded => loaded).length;
            const totalCount = Object.keys(modelsLoaded).length;
            console.log(`加载进度: ${loadedCount}/${totalCount}`);
        }

        // 固定步长推进游戏逻辑
        if (gameStateInternal === 'playing') {
            accumulator += Math.min(delta, 0.1);
            while (accumulator >= FIXED_DT) {
                gameStep(FIXED_DT);
                accumulator -= FIXED_DT;
            }
        }

        // 更新UI显示
        updateGameUI();

        // FPS 计算
        if (!window.__fpsSamples) {
            window.__fpsSamples = { sum: 0, count: 0, time: 0 };
        }
        const s = window.__fpsSamples;
        s.sum += (1 / Math.max(0.000001, delta));
        s.count += 1;
        s.time += delta;
        if (s.time >= 0.5) {
            const avgFps = Math.round(s.sum / s.count);
            gameState.value.fps = avgFps;
            const fpsEl = document.getElementById('fps');
            if (fpsEl) fpsEl.textContent = String(avgFps);
            s.sum = 0;
            s.count = 0;
            s.time = 0;
        }

        renderer.render(scene, camera);
        animationFrame = requestAnimationFrame(animate);
    }

    // 更新游戏UI
    function updateGameUI() {
        // 更新倒计时显示
        if (gameStateInternal === 'countdown') {
            const timeEl = document.querySelector('.time');
            if (timeEl) {
                timeEl.textContent = `${gameState.value.countdown}s`;
                timeEl.style.fontSize = '48px';
                timeEl.style.color = '#ff0000';
            }
        } else if (gameStateInternal === 'playing') {
            const timeEl = document.querySelector('.time');
            if (timeEl) {
                timeEl.textContent = `${gameState.value.gameTime}s`;
                timeEl.style.fontSize = '24px';
                timeEl.style.color = '#ffffff';
            }
        }
    }

    // 输入处理
    const handleKeyDown = (e) => {
        keys[e.key] = true;

        if (gameStateInternal === 'playing') {
            if (e.key === 'ArrowLeft' || e.key === 'a' || e.key === 'A') {
                playerX -= 0.2;
                playerX = clampPlayerX(playerX);
            }
            if (e.key === 'ArrowRight' || e.key === 'd' || e.key === 'D') {
                playerX += 0.2;
                playerX = clampPlayerX(playerX);
            }
        }
    };

    const handleKeyUp = (e) => {
        keys[e.key] = false;
    };

    // 触摸拖动控制
    let isDragging = false;
    let lastTouchX = 0;

    const handleTouchStart = (e) => {
        if (gameStateInternal === 'gameOver') {
            restartGame();
            return;
        }
        if (gameStateInternal === 'playing') {
            isDragging = true;
            lastTouchX = e.touches[0].clientX;
        }
    };

    const handleTouchMove = (e) => {
        if (gameStateInternal !== 'playing' || !isDragging) return;

        e.preventDefault();
        const currentTouchX = e.touches[0].clientX;
        const deltaX = currentTouchX - lastTouchX;

        const sensitivity = 0.01;
        playerX += deltaX * sensitivity;
        playerX = clampPlayerX(playerX);

        lastTouchX = currentTouchX;
    };

    const handleTouchEnd = () => {
        isDragging = false;
    };

    const handleClick = () => {
        if (gameStateInternal === 'gameOver') {
            restartGame();
        }
    };

    const handleResize = () => {
        camera.aspect = window.innerWidth / window.innerHeight;
        camera.updateProjectionMatrix();
        renderer.setSize(window.innerWidth, window.innerHeight);
        renderer.setPixelRatio(Math.min(window.devicePixelRatio || 1, 2));
    };

    // 开始游戏
    const startGame = () => {
        gameState.value.isRunning = true;
        gameState.value.isGameOver = false;
        gameState.value.gameStartTime = Date.now();
        gameStateInternal = 'playing';

        console.log('3D跑酷游戏开始');
    };

    // 添加事件监听
    const addEvents = () => {
        document.addEventListener('keydown', handleKeyDown);
        document.addEventListener('keyup', handleKeyUp);
        document.addEventListener('touchstart', handleTouchStart);
        document.addEventListener('touchmove', handleTouchMove);
        document.addEventListener('touchend', handleTouchEnd);
        document.addEventListener('click', handleClick);
        window.addEventListener('resize', handleResize);
    };

    // 移除事件监听
    const removeEvents = () => {
        document.removeEventListener('keydown', handleKeyDown);
        document.removeEventListener('keyup', handleKeyUp);
        document.removeEventListener('touchstart', handleTouchStart);
        document.removeEventListener('touchmove', handleTouchMove);
        document.removeEventListener('touchend', handleTouchEnd);
        document.removeEventListener('click', handleClick);
        window.removeEventListener('resize', handleResize);

        if (animationFrame) {
            cancelAnimationFrame(animationFrame);
        }
    };

    return {
        gameState,
        loadAssets,
        initGame,
        startGame,
        addEvents,
        removeEvents
    };
}
