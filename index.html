<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0,minimal-ui:ios" name="viewport">
    <title>中烟月满归途徽商礼韵 中秋探乡记</title>
    <link rel="stylesheet" href="https://ztimg.hefei.cc/static/common/css/style.min.css">
    <link rel="stylesheet" href="https://ztimg.hefei.cc/static/common/css/animate.css">
    <link rel="stylesheet" href="https://ztimg.hefei.cc/static/common/css/<EMAIL>">
    <link rel="stylesheet" href="css/index.css?v={php} echo mt_rand(1000,999999);{/php}">
    <script defer src="css/polyfill.js?v={php} echo mt_rand(1000,999999);{/php}"></script>
    <!-- <script src="https://unpkg.com/vconsole@latest/dist/vconsole.min.js"></script> 
    <script> var vConsole = new window.VConsole(); </script> -->
    <style>
        [v-cloak] {
            display: none;
        }
    </style>
</head>

<body>
    <div id="app" class="warp" v-cloak>
        <div class="musicbtn" :class="{ on:on }"  @click="bgClick"></div>
        <img src="img/logo.png" class="logo">
        <!-- 首页 -->
        <div class="page fc" :class="{blur:show}" v-if="page===1">
            <div class="rotation animate__animated animate__fadeIn">
                <van-swipe ref="swipe" class="my-swipe" :autoplay="2000" :show-indicators="false" indicator-color="white" vertical>
                    <van-swipe-item v-for="(item,index) in handleRotation">
                        <p>{{item[0]}}<span>{{item[1]}}</span>{{item[2]}}</p>
                    </van-swipe-item>
                </van-swipe>
            </div>
            <img class="title animate__animated animate__zoomIn" src="img/title.png">
            <div class="start pulsate-bck2" @click="start"></div>
            <div class="button_container">
                <img src="img/button2.png" class="button animate__animated animate__fadeInLeft" @click="page=2">
                <img src="img/button3.png" class="button animate__animated animate__fadeInRight" @click="page=3">
            </div>
        </div>
        <!-- 活动规则页 -->
        <div class="page fc" :class="{blur:show}" v-if="page===2">
            <img src="img/title.png" class="title2 animate__animated animate__zoomIn">
            <div class="area area1">
                <div class="rule" v-html="startData.rule"></div>
                <img src="img/button4.png" class="back animate__animated animate__fadeInUp" @click="page=1">
            </div>
        </div>
        <!-- 我的奖品页 -->
        <div class="page fc" :class="{blur:show}" v-if="page===3">
            <img src="img/title.png" class="title2 animate__animated animate__zoomIn">
            <div class="area area2">
                <div class="prize">
                    <div class="info">
                        <img src="img/jptit1.png" class="jptit mt5">
                        <p class="p2 p3 mt5" v-if="startData.prize">
                            {{startData.prizeTime}}：{{startData.ad}}</p>
                        <div class="p2" v-if="startData.prize">{{startData.prize}}</div>
                        <p class="p2 p1 mt5" v-else>暂未中奖</p>
                    </div>
                    <div class="info">
                        <img src="img/jptit2.png" class="jptit">
                        <p class="p2 mt5">{{startData.userInfo.name}}&nbsp;&nbsp;{{startData.userInfo.phone}}</p>
                        <p class="p2">{{startData.userInfo.area.split(',').join('')}}</p>
                        <p class="p2">{{startData.userInfo.address}}</p>
                    </div>
                    <img src="img/edit.png" class="edit" @click="edit">
                </div>
                <img src="img/button4.png" class="back animate__animated animate__fadeInUp" @click="page=1">
            </div>
        </div>
        <!-- 登记信息页 -->
        <div class="page fc" :class="{blur:show}" v-if="page===4">
            <div class="area area3">
                <form class="form">
                    <div class="form-item">
                        <label>姓   名:</label>
                        <input type="text" v-model="form.name">
                    </div>
                    <div class="form-item">
                        <label>联系方式:</label>
                        <input type="number" v-model="form.phone">
                    </div>
                    <div class="form-item fs">
                        <label>邮寄地址:</label>
                        <div class="right" @click="focus">
                            <input type="text" placeholder="选择省" v-model="form.area.split(',')[0]" readonly>
                            <input type="text" placeholder="选择市" v-model="form.area.split(',')[1]" readonly>
                            <input type="text" placeholder="选择区" v-model="form.area.split(',')[2]" readonly>
                        </div>
                    </div>
                    <div class="form-item">
                        <label>街道地址:</label>
                        <input type="text" v-model="form.address" @keyup.enter="submit">
                    </div>
                    <div class="form-footer">
                        <p><span>免责声明:</span>本活动专题收集的所有信息仅供发放活动奖品使用，在未经得本人同意情况下绝对不会将您的任何资料以任何方式泄露给第三方。由于您自身原因如共享登录账号等导致的个人信息披露，活动方概不负责。
                        </p>
                    </div>
                    <img src="img/button6.png" class="back2 animate__animated animate__fadeInUp" @click="submit">
                </form>
                <van-popup v-model:show="popupShow" round position="bottom">
                    <van-picker show-toolbar title="请选择地区" :columns="options" default-index="11"
                        v-model="selectedValues" @cancel="popupShow = false" @confirm="onConfirm"></van-picker>
                </van-popup>
            </div>
        </div>
        <!-- 游戏页 -->
        <div class="page bj2 fc" :class="{blur:show}" v-if="page===5">
            <div class="game_area">
                <!-- Three.js Canvas 容器 -->
                <div id="canvas-container" ref="canvasContainer"></div>

                <!-- 游戏信息UI -->
                <div class="gameInfo">
                    <div>距离: <span id="distance">{{(gameState && gameState.distance) || 0}}</span>m</div>
                    <div>FPS: <span id="fps">{{(gameState && gameState.fps) || 0}}</span></div>
                    <div v-if="gameState && gameState.gamePhase === 'countdown'">倒计时: {{(gameState && gameState.countdown) || 3}}秒</div>
                    <div v-if="gameState && gameState.gamePhase === 'playing'">剩余时间: {{(gameState && gameState.gameTime) || 60}}秒</div>
                    <div v-if="gameState && gameState.gamePhase === 'playing'">场景: {{(gameState && gameState.currentScene) || 1}}</div>
                </div>
                <div class="time">
                    <span v-if="gameState && gameState.gamePhase === 'countdown'">{{(gameState && gameState.countdown) || 3}}</span>
                    <span v-else>{{(gameState && gameState.gameTime) || 60}}s</span>
                </div>
                <!-- 游戏结束界面 -->
                <div v-if="gameState && gameState.showGameOver">
                    <h1>游戏结束!</h1>
                    <p>跑了 <span id="finalDistance">{{(gameState && gameState.finalDistance) || 0}}</span>米</p>
                    <p>点击/触摸屏幕重新开始</p>
                </div>
            </div>
        </div>
        <!-- 提交成功弹窗 -->
        <transition name="van-fade">
            <div class="mask fc" v-if="show===1">
                <div class="popup popup1">
                    <img src="img/button4.png" class="back" @click="reload">
                </div>
            </div>
        </transition>
        <!-- 无机会弹窗 -->
        <transition name="van-fade">
            <div class="mask fc" v-if="show===2">
                <div class="popup popup2">
                    <img src="img/close.png" class="close2" @click="reload">
                </div>
            </div>
        </transition>
        <!-- 游戏成功弹窗 -->
        <transition name="van-fade">
            <div class="mask fc" v-if="show===3">
                <div class="popup popup3">
                    <img src="img/button8.png" class="back" @click="getPrize">
                </div>
            </div>
        </transition>
        <!-- 游戏失败弹窗 -->
        <transition name="van-fade">
            <div class="mask fc" v-if="show===4">
                <div class="popup popup4">
                    <img src="img/button9.png" class="back" @click="reload">
                </div>
            </div>
        </transition>
        <!-- 中奖弹窗 -->
        <transition name="van-fade">
            <div class="mask fc" v-if="show===5">
                <div class="popup popup5">
                    <div class="p3" v-html="prizeData.ad"></div>
                    <div class="p4">抽中{{prizeData.prize}}</div>
                    <img src="img/button10.png" class="back" @click="goForm">
                </div>
            </div>
        </transition>
        <!-- 未中奖弹窗 -->
        <transition name="van-fade">
            <div class="mask fc" v-if="show===6">
                <div class="popup popup6">
                    <img src="img/button4.png" class="back" @click="reload">
                </div>
            </div>
        </transition>
    </div>
    
    <!-- <script src="https://ztimg.hefei.cc/static/common/js/libs/vue.js"></script> -->
    <script src="https://ztimg.hefei.cc/static/common/js/libs/<EMAIL>"></script>
    <script src="https://ztimg.hefei.cc/static/common/js/libs/<EMAIL>"></script>
    <script src="https://ztimg.hefei.cc/static/common/js/libs/jquery-3.6.0.min.js"></script>
    <script src="https://ztimg.hefei.cc/static/common/js/libs/howler.min.js"></script>
    <script src="https://ztimg.hefei.cc/static/common/js/libs/preloadjs.min.js"></script>
    <script src="https://ztimg.hefei.cc/static/common/js/libs/dayjs.min.js"></script>
    <script src="https://ztimg.hefei.cc/static/common/js/libs/html2canvas.min.js"></script>
    <script src="https://ztimg.hefei.cc/static/common/js/libs/three.min.js"></script>
    <script src="https://ztimg.hefei.cc/static/common/js/hook/hook1.js"></script>
    <script src="https://ztimg.hefei.cc/static/common/js/hook/hook2.js"></script>
    <script>
        const { createApp, ref, watch, nextTick } = Vue
    </script>
    <script src="game2.js?v={php} echo mt_rand(1000,999999);{/php}"></script>
    <script>
        let rotation = [];
        window.startData = {
            rotation:rotation,
            jihui: '5',
            prize: '{$prize}',
            ad: '{$ad}',
            prizeTime: '{$prizeTime}',
            userInfo: {
                name: '{$name}',
                phone: '{$phone}',
                area: '{$area}',
                address: '{$address}',
            },
            nickname: '{$nickname}',
            avatar: '{$headimgurl}',
            wlq:'{$wlq}',
            endtime: '{$endtime}',
            writeFlag: '{$writeFlag}',
            rule: `活动时间：2025年9月30日—2025年10月4日<br>
                活动规则：<br>
                1、用户点击开始进入游戏界面，倒计时3秒后游戏开始，主页面出现NPC触发对话。每个NPC提出情境问题，用户需从3个选项中选择回应。<br>
                选择不同的回答可获得加速/减速/无变化三种效果。在一分钟内顺利通过三个场景，可获得一次抽奖机会。<br>
                2、每个用户每天共有3次挑战机会，中途退出视为消耗1次机会。<br>
                3、在挑战成功的用户中随机抽选1000名，派送礼品一份，奖品多多快来参与~ <br>
                4、用户在填写信息时，需填写详细联系方式（姓名、电话号码和邮寄地址），因个人原因（包括但不限于地址电话填错、电话无法接通、地址填写不完整等）导致快递退回，均不予补发处理。<br>
                5、在同一次活动中，姓名、手机号、收货地址中有一项及以上相同时，默认只寄发首次中奖奖品。物流相关事宜（包括签收、破损、丢件等问题），由中奖者自行和快递公司协商处理。<br>
                6、活动过程中有任何问题可发送疑问至“甜润世界”微信公众号后台。`,
        }
        /*
        将game2.js的游戏删除金币和障碍物，倒计时3秒后游戏开始，游戏倒计时60s，在归途中会在3个场景中依次遇到3个npc,每个场景地面和街道要使用CONFIG里的对应图片和模型，人物靠近每个npc的时候，npc头上出现dialog气泡框，气泡框内显示他提出的问题，人物遇到npc的同时暂停游戏，html内弹出选择框img/selectbox.png,弹框写在game_area内，用户需从3个选项中选择回应,选择不同的回答可获得加速/减速/无变化三种效果,同时player头上弹出气泡框dialog2.png显示选中的回答，回答功能的数据结构用question。在一分钟内顺利通过三个场景，视为游戏成功，执行onGameEnd(true),否则为onGameEnd(false),生成模型的函数也要改为场景1街道模型然后1个城门然后3个场景2街道模型然后3个场景3街道模型。你来帮我实现以上功能*/
        const question = [
            {
                title:'行行好，老爷，赏两个\n铜钱买块饼吃吧…',
                people:'img/people1.png',
                dialog:'img/dialog1.png',
                desc:'乞讨老人',
                option:[
                    {text:'给您些银钱和干粮，\n望您中秋过得安康。',speed:1},
                    {text:'拿去吧。',speed:0},
                    {text:'你这老朽，莫要挡路！',speed:-1},
                ]
            },
            {
                title:'客官，赶路辛苦，\n喝碗粗茶解解乏？',
                people:'img/people2.png',
                dialog:'img/dialog1.png',
                desc:'乡间茶亭卖茶人',
                option:[
                    {text:'也好，歇息片刻，\n多谢店家。',speed:1},
                    {text:'不必了，赶路。',speed:0},
                    {text:'你这野摊的茶，怎能入口？',speed:-1},
                ]
            },
            {
                title:'先生请了！巷道狭窄，\n愿先行退让，请先生先过。',
                people:'img/people3.png',
                dialog:'img/dialog1.png',
                desc:'徽商车队',
                option:[
                    {text:'诸位行商辛苦，\n货担为重，理当先行。',speed:1},
                    {text:'多谢，那便承让了。',speed:0},
                    {text:'自然该让我先过。',speed:-1},
                ]
            },
        ]
        const app = createApp({
            setup() {
                const { bgMusic,on, bgClick } = useBgMusic('123.mp3')//调用景音乐
                setMockPage && setMockPage()//添加案例提示语
                const page = ref(1) //控制页面
                const show = ref(0) //控制弹窗
                const { userInfo, endtime, writeFlag } = startData
                const opportunity = ref((+startData.jihui)) //控制机会
                const handleRotation = startData.rotation.map(item => item.split(',')) // 设置顶部获奖数据
                const start = () => {
                    if (endtime === '1') return vantAlert('活动未开始')
                    if (endtime === '2') return vantAlert('活动已结束')
                    if (opportunity.value >= 1) {
                        page.value = 5
                        opportunity.value--
                        defaultFetch('gamestart',{}, { status: 1 })
                    } else {
                        show.value = 2
                    }
                }

                const edit = () => {
                    if (writeFlag === 0) {
                        return vantAlert('活动已结束');
                    } else {
                        goForm()
                    }
                }
                const goForm = () => {
                    page.value = 4
                    show.value = 0
                }

                // 登记信息功能
                const { form, popupShow, options, focus, onConfirm, check, selectedValues } = createdForm()
                form.value = userInfo
                const submit = throttle(async () => {
                    if (!check()) return
                    const res = await defaultFetch('action', Object.assign({ act: 'sub' }, form.value), { status: 1 })
                    if (res.status == 1) {
                        show.value = 1
                    } else {
                        vantAlert(res.msg)
                    }
                })
                // 刷新页面功能
                const { reload, savePage } = useReload()
                if (savePage) { 
                    page.value = +savePage
                }
                // 判断是否是初次进入网页,执行预加载
                const { loadStart, progress, progressShow, startFlag } = cheackStartPopup([])
                loadStart()
                if(startFlag){
                    page.value = 2
                }
                // 检查未领取
                if (startData.wlq === '1') {
                    vant.showConfirmDialog({
                        title: '温馨提示',
                        message: '您已中奖，请尽快完善领奖信息！',
                        confirmButtonText: '去领取',
                        cancelButtonText: '不领取'
                    }).then(() => {
                        show.value = 0
                        page.value = 4
                    })
                }

                const gameEnd = throttle(async (e) => {
                    const res = await defaultFetch('gameEnd', {
                        cg: e ? 1 : 0,
                    }, { status: 1 })
                    if (res.status === 1) {
                        show.value = 3
                    } else if(res.status === 2) {
                        show.value = 4
                    } else {
                        vantAlert(res.msg, reload)
                    }
                })

                // 抽奖逻辑
                const prizeData = ref({ prize: startData.prize, ad: startData.ad, prizeType: 0 })
                const getPrize = throttle(async () => {
                    const res = await defaultFetch('getprize', {}, { status: 1, msg: '抽中奖品', data: { prize: '水笔一支', ad: '甜润迎新，笔笔生花', prizeType: 1 } })
                    if (res.status === 1) {
                        show.value = 5
                        prizeData.value = res.data
                    } else if (res.status === 2) {
                        show.value = 6 //未中奖
                    } else {
                        vantAlert(res.msg, reload)
                    }
                })

                // 游戏逻辑
                const canvasContainer = ref(null)
                const {
                    gameState,
                    startGame,
                    initGame,
                    loadAssets,
                    addEvents,
                    removeEvents,
                } = useRunnerGame(canvasContainer, gameEnd)
                // 监听页面变化，当进入游戏页面时初始化游戏
                watch(page, async (newPage) => {
                    if (newPage === 5) {
                        setTimeout(async() => {
                            vant.showLoadingToast({
                                message: '游戏加载中...',
                                forbidClick: true,
                                duration:0,
                            });
                            // 先初始化游戏场景
                            await initGame()
                            // 然后加载资源
                            await loadAssets()
                            // 添加事件监听
                            addEvents()
                            // 资源加载完成后，游戏会自动开始
                            vant.closeToast()
                        }, 100);
                    } else {
                        // 离开游戏页面时清理资源
                        removeEvents()
                    }
                },{
                    immediate:true
                })

                return {
                    startData, page, show,
                    handleRotation, edit, goForm,
                    on, bgClick,
                    start, submit, reload,
                    form, popupShow, options, focus, onConfirm, check, selectedValues,
                    prizeData, getPrize,
                    canvasContainer, gameState
                }
            },
        });
        app.use(vant);
        app.mount('#app');
    </script>
        <!--分享-->
{include file="share"/}
</body>

</html>