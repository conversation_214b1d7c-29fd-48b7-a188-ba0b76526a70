@font-face {
  font-family: '思源宋体';
  src: url(https://ztimg.hefei.cc/static/common/fonts/思源黑体.otf);
}
body,
div,
h1,
h2,
h3,
h4,
h5,
h6,
html,
li,
p,
span {
  font-size: 2.1vh;
}
.musicbtn {
  width: 6vh;
  height: 6vh;
  position: absolute;
  top: 2.16vh;
  right: 1.68vh;
  background: url(../img/music.png) no-repeat center center / contain;
  z-index: 11;
}
.logo {
  width: 11.03vh;
  position: absolute;
  top: 2.16vh;
  left: 1.68vh;
  z-index: 11;
}
.warp {
  margin: 0 auto;
  min-height: 101.95vh;
  height: 100vh;
  width: 59.97vh;
  display: flex;
  justify-content: center;
  align-items: center;
  font-family: '思源宋体';
  background: url(../img/bj2.jpg) no-repeat center center / 100% 100%;
  position: relative;
}
.warp .swipe_container {
  width: 100%;
  height: 100%;
}
.warp .page {
  width: 100%;
  height: 100%;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  z-index: 2;
  color: #f3e3bb;
  background: url(../img/bj.jpg) no-repeat center center / 100% auto;
}
.warp .page .rotation {
  margin-top: 6vh;
  width: 35.98vh;
  height: 4.2vh;
  border-radius: 2.1vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  z-index: 3;
}
.warp .page .rotation .van-swipe {
  height: 5.28vh;
}
.warp .page .rotation .van-swipe .van-swipe-item {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}
.warp .page .rotation p {
  text-align: center;
  white-space: nowrap;
  font-size: 2.4vh;
}
.warp .page .rotation p span {
  font-size: 2.4vh;
}
.warp .page .title {
  margin-top: 1.2vh;
  width: 47.5vh;
  z-index: 2;
}
.warp .page .title2 {
  margin-top: 1.8vh;
  width: 35.98vh;
  z-index: 3;
}
.warp .page .start {
  margin-top: 33.58vh;
  width: 13.19vh;
  height: 13.19vh;
  flex-shrink: 0;
  background: url(../img/start.png) no-repeat center center / 100% 100%;
  z-index: 2;
}
.warp .page .start::before {
  content: '';
  position: absolute;
  top: -10%;
  left: -10%;
  width: 120%;
  height: 120%;
  background: url(../img/start_area.png) no-repeat center center / 100% 100%;
  animation: scaleLight 1s ease-in-out infinite;
}
.warp .page .bg2 {
  width: 59.97vh;
  position: absolute;
  left: 0;
  bottom: 0;
  pointer-events: none;
}
.warp .page .bg3 {
  width: 59.97vh;
  position: absolute;
  left: 0;
  top: 0;
  pointer-events: none;
}
.warp .page .button_container {
  position: relative;
  z-index: 2;
  margin-top: 3vh;
  display: flex;
  justify-content: space-between;
  width: 100%;
  padding: 0 3.6vh;
}
.warp .page .button_container .button {
  width: 19.35vh;
}
.warp .page .game_area {
  width: 59.97vh;
  height: 106.67vh;
  display: flex;
  justify-content: center;
  align-items: center;
}
.warp .page .game_area .time {
  width: 12.95vh;
  height: 4.72vh;
  background: url(../img/time.png) no-repeat center center / 100% 100%;
  color: #d63067;
  position: absolute;
  top: 6vh;
  left: 3vh;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 3vh;
  padding-left: 3.6vh;
}
.warp .page .game_area .score_area {
  width: 50.37vh;
  height: 2.56vh;
  border: 1px solid #f1e26f;
  box-shadow: 0px 0px 10px 0px rgba(255, 255, 255, 0.5);
  border-radius: 1.28vh;
  position: absolute;
  top: 13.19vh;
  display: flex;
  align-items: center;
}
.warp .page .game_area .score_area::before {
  content: '甜润时刻';
  position: absolute;
  top: -4.8vh;
  left: 50%;
  transform: translateX(-50%);
  font-size: 2.7vh;
  font-weight: bold;
  color: #ff6ec7;
  text-shadow: 0 0 8px rgba(255, 110, 199, 0.6);
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.3s ease;
  z-index: 2;
  will-change: opacity, transform;
}
.warp .page .game_area .score_area::after {
  content: '';
  width: 5.6vh;
  height: 5.6vh;
  background: url(../img/qiu.png) no-repeat center center / 100% 100%;
  position: absolute;
  right: -2.8vh;
  transition: 0.3s;
  filter: grayscale(100%);
}
.warp .page .game_area .score_area.full::before {
  opacity: 1;
  animation: sweetMomentGlow 2.5s ease-in-out infinite alternate;
}
.warp .page .game_area .score_area.full::after {
  filter: grayscale(0%);
}
.warp .page .game_area .score_area .score {
  background-color: #cf5cdb;
  position: absolute;
  left: 0;
  height: 100%;
  border-radius: 1.28vh;
  display: flex;
  align-items: center;
  transition: 0.3s;
}
.warp .page .game_area .score_area .score.full {
  background: linear-gradient(90deg, #cf5cdb, #ff6ec7, #cf5cdb);
  background-size: 200% 100%;
  animation: scoreGlow 2s ease-in-out infinite alternate, gradientShift 3s linear infinite;
  will-change: box-shadow, background-position;
}
@keyframes sweetMomentGlow {
  0% {
    color: #ff6ec7;
    text-shadow: 0 0 8px rgba(255, 110, 199, 0.6);
    transform: translateX(-50%) scale(1);
  }
  100% {
    color: #fff;
    text-shadow: 0 0 12px rgba(255, 110, 199, 0.8);
    transform: translateX(-50%) scale(1.05);
  }
}
@keyframes scoreGlow {
  0% {
    box-shadow: 0 0 8px #cf5cdb;
  }
  100% {
    box-shadow: 0 0 15px #ff6ec7, 0 0 25px rgba(255, 110, 199, 0.5);
  }
}
@keyframes gradientShift {
  0% {
    background-position: 0% 50%;
  }
  100% {
    background-position: 200% 50%;
  }
}
.warp .page .game_area canvas {
  width: 100%;
  height: 100%;
}
.warp .bj2 {
  background-image: url(../img/bj2.jpg);
}
.blur {
  filter: blur(0.6vh);
}
.fc {
  justify-content: center;
}
.area {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  z-index: 2;
  background: url(../img/area.png) no-repeat center center / 100% 100%;
}
.area .back {
  position: absolute;
  bottom: -2.4vh;
  width: 23.99vh;
  margin-right: 2.4vh;
}
.area .back2 {
  position: absolute;
  bottom: -3.6vh;
  width: 23.99vh;
}
.area .submit {
  position: absolute;
  bottom: -9vh;
  width: 23.99vh;
}
.area .rule {
  width: 100%;
  padding: 0 3.6vh;
  margin: 10.19vh 0 6vh;
  flex: 1;
  overflow-y: auto;
  line-height: 1.5;
  text-align: justify;
  letter-spacing: -0.06vh;
  position: relative;
}
.area .prize {
  margin-top: 7.8vh;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.area .prize .mt5 {
  margin-top: 1.2vh;
}
.area .prize .info {
  padding: 2.4vh 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  min-height: 9.6vh;
  width: 41.98vh;
}
.area .prize .info:first-child {
  border-bottom: 1px dashed #f3e3bb;
}
.area .prize .info .p2 {
  font-size: 3vh;
  line-height: 4.2vh;
  max-width: 44.98vh;
  text-align: center;
}
.area .prize .info .jptit {
  width: 17.67vh;
  margin-bottom: 1.2vh;
}
.area .prize .edit {
  width: 15.59vh;
}
.area .form {
  width: 100%;
  padding: 16.79vh 3vh 0;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.area .form .form-item {
  margin-left: 0;
  margin-bottom: 4.2vh;
  display: flex;
  align-items: center;
}
.area .form .form-item label {
  width: 14.39vh;
  font-weight: bold;
  font-size: 3vh;
  white-space: nowrap;
  color: #000;
  flex-shrink: 0;
}
.area .form .form-item div input {
  margin-bottom: 1.8vh;
}
.area .form .form-item div input:nth-last-child(1) {
  margin-bottom: 0;
}
.area .form .form-item .right {
  flex-shrink: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.area .form .form-item input {
  margin-left: 0.48vh;
  padding-left: 1.52vh;
  width: 27.59vh;
  height: 4.64vh;
  border: 1px #000 solid;
  flex-shrink: 0;
  opacity: 1;
  color: #000;
  font-size: 3vh;
}
.area .form .form-item input::-webkit-input-placeholder {
  color: #000;
  opacity: 0.6;
}
.area .form .form-item input:-moz-placeholder {
  color: #000;
  opacity: 0.6;
}
.area .form .form-item input::-moz-placeholder {
  color: #000;
  opacity: 0.6;
}
.area .form .form-item input:-ms-input-placeholder {
  color: #000;
  opacity: 0.6;
}
.area .form .form-item #getArea {
  opacity: 0;
  position: absolute;
  z-index: -1;
}
.area .form .form-footer {
  margin-top: 1.2vh;
  display: flex;
  color: #000;
}
.area .form .form-footer p {
  font-size: 1.8vh;
  line-height: 1.5;
  text-align: justify;
}
.area .form .form-footer p span {
  font-weight: bold;
  font-size: 1.8vh;
}
.area .form .button {
  margin-top: -3vh;
  width: 18.23vh;
}
.area .form .fs {
  align-items: flex-start;
}
.area .form .fs label {
  margin-top: 0.3vh;
}
.area1 {
  margin-top: 0.6vh;
  width: 54.13vh;
  height: 69.49vh;
  background: url(../img/area1.png) no-repeat center center / 100% 100%;
}
.area2 {
  margin-top: 0.6vh;
  width: 53.09vh;
  height: 67.49vh;
  background: url(../img/area2.png) no-repeat center center / 100% 100%;
}
.area3 {
  margin-top: 0.6vh;
  width: 51.33vh;
  height: 81.64vh;
  background: url(../img/area3.png) no-repeat center center / 100% 100%;
}
.mask {
  z-index: 10;
  position: fixed;
  top: 0;
  left: 0;
  min-height: 100vh;
  width: 59.97vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  background: url(../img/mask.png) no-repeat center center / 100% 100%;
  transform: translateX(-50%);
  left: 50%;
  color: #d63067;
}
.mask .countdown {
  height: 35.5vh;
}
.mask .popup {
  margin-top: -0.6vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: relative;
}
.mask .popup .back {
  width: 23.99vh;
  position: absolute;
  bottom: -3.6vh;
  margin-right: 2.4vh;
}
.mask .popup1 {
  width: 51.41vh;
  height: 33.98vh;
  background: url(../img/popup1.png) no-repeat center top / 100% 100%;
}
.mask .popup2 {
  width: 51.41vh;
  height: 33.98vh;
  background: url(../img/popup2.png) no-repeat center top / 100% 100%;
}
.mask .popup2 .close2 {
  width: 4.96vh;
  position: absolute;
  top: -1.8vh;
  right: -1.8vh;
}
.mask .popup3 {
  width: 51.33vh;
  height: 36.46vh;
  background: url(../img/popup3.png) no-repeat center top / 100% 100%;
}
.mask .popup4 {
  width: 51.33vh;
  height: 36.46vh;
  background: url(../img/popup4.png) no-repeat center top / 100% 100%;
}
.mask .popup5 {
  width: 51.33vh;
  height: 36.46vh;
  background: url(../img/popup5.png) no-repeat center top / 100% 100%;
  padding-top: -2.4vh;
}
.mask .popup5 .p3 {
  font-size: 4.5vh;
  white-space: nowrap;
  font-weight: bold;
}
.mask .popup5 .p4 {
  font-size: 4.2vh;
  white-space: nowrap;
}
.mask .popup6 {
  width: 51.33vh;
  height: 36.46vh;
  background: url(../img/popup6.png) no-repeat center top / 100% 100%;
}
@keyframes scaleLight {
  0%,
  100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.6);
    opacity: 0;
  }
}
