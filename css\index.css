@font-face {
  font-family: '思源宋体';
  src: url(https://ztimg.hefei.cc/static/common/fonts/思源宋体.otf);
}
body,
div,
h1,
h2,
h3,
h4,
h5,
h6,
html,
li,
p,
span {
  font-size: 3.5vw;
}
.musicbtn {
  width: 10vw;
  height: 10vw;
  position: absolute;
  top: 3.6vw;
  right: 2.8vw;
  background: url(../img/music.png) no-repeat center center / contain;
  z-index: 11;
}
.logo {
  width: 18.4vw;
  position: absolute;
  top: 3.6vw;
  left: 2.8vw;
  z-index: 11;
}
.warp {
  margin: 0 auto;
  min-height: 170vw;
  height: 100vh;
  width: 100vw;
  display: flex;
  justify-content: center;
  align-items: center;
  font-family: '思源宋体';
  background: url(../img/bj2.jpg) no-repeat center center / 100% 100%;
  position: relative;
}
.warp .swipe_container {
  width: 100%;
  height: 100%;
}
.warp .page {
  width: 100%;
  height: 100%;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  z-index: 2;
  color: #f3e3bb;
  background: url(../img/bj.jpg) no-repeat center center / 100% auto;
}
.warp .page .rotation {
  margin-top: 10vw;
  width: 60vw;
  height: 7vw;
  border-radius: 3.5vw;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  z-index: 3;
}
.warp .page .rotation .van-swipe {
  height: 8.8vw;
}
.warp .page .rotation .van-swipe .van-swipe-item {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}
.warp .page .rotation p {
  text-align: center;
  white-space: nowrap;
  font-size: 4vw;
}
.warp .page .rotation p span {
  font-size: 4vw;
}
.warp .page .title {
  margin-top: 2vw;
  width: 79.2vw;
  z-index: 2;
}
.warp .page .title2 {
  margin-top: 3vw;
  width: 60vw;
  z-index: 3;
}
.warp .page .start {
  margin-top: 56vw;
  width: 22vw;
  height: 22vw;
  flex-shrink: 0;
  background: url(../img/start.png) no-repeat center center / 100% 100%;
  z-index: 2;
}
.warp .page .start::before {
  content: '';
  position: absolute;
  top: -10%;
  left: -10%;
  width: 120%;
  height: 120%;
  background: url(../img/start_area.png) no-repeat center center / 100% 100%;
  animation: scaleLight 1s ease-in-out infinite;
}
.warp .page .bg2 {
  width: 100vw;
  position: absolute;
  left: 0;
  bottom: 0;
  pointer-events: none;
}
.warp .page .bg3 {
  width: 100vw;
  position: absolute;
  left: 0;
  top: 0;
  pointer-events: none;
}
.warp .page .button_container {
  position: relative;
  z-index: 2;
  margin-top: 5vw;
  display: flex;
  justify-content: space-between;
  width: 100%;
  padding: 0 6vw;
}
.warp .page .button_container .button {
  width: 32.2667vw;
}
.warp .page .game_area {
  width: 100vw;
  height: 177.8667vw;
  display: flex;
  justify-content: center;
  align-items: center;
}
.warp .page .game_area .gameInfo {
  position: absolute;
  top: 15vw;
  left: 2vw;
  color: #000;
}
.warp .page .game_area .time {
  width: 18.5333vw;
  height: 20vw;
  background: url(../img/time.png) no-repeat center center / 100% 100%;
  color: #000;
  position: absolute;
  top: 10vw;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 6vw;
  font-weight: bold;
  padding-bottom: 3vw;
}
.warp .page .game_area .score_area {
  width: 84vw;
  height: 4.267vw;
  border: 1px solid #f1e26f;
  box-shadow: 0px 0px 10px 0px rgba(255, 255, 255, 0.5);
  border-radius: 2.1333vw;
  position: absolute;
  top: 22vw;
  display: flex;
  align-items: center;
}
.warp .page .game_area .score_area::before {
  content: '甜润时刻';
  position: absolute;
  top: -8vw;
  left: 50%;
  transform: translateX(-50%);
  font-size: 4.5vw;
  font-weight: bold;
  color: #ff6ec7;
  text-shadow: 0 0 8px rgba(255, 110, 199, 0.6);
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.3s ease;
  z-index: 2;
  will-change: opacity, transform;
}
.warp .page .game_area .score_area::after {
  content: '';
  width: 9.3333vw;
  height: 9.3333vw;
  background: url(../img/qiu.png) no-repeat center center / 100% 100%;
  position: absolute;
  right: -4.6667vw;
  transition: 0.3s;
  filter: grayscale(100%);
}
.warp .page .game_area .score_area.full::before {
  opacity: 1;
  animation: sweetMomentGlow 2.5s ease-in-out infinite alternate;
}
.warp .page .game_area .score_area.full::after {
  filter: grayscale(0%);
}
.warp .page .game_area .score_area .score {
  background-color: #cf5cdb;
  position: absolute;
  left: 0;
  height: 100%;
  border-radius: 2.1333vw;
  display: flex;
  align-items: center;
  transition: 0.3s;
}
.warp .page .game_area .score_area .score.full {
  background: linear-gradient(90deg, #cf5cdb, #ff6ec7, #cf5cdb);
  background-size: 200% 100%;
  animation: scoreGlow 2s ease-in-out infinite alternate, gradientShift 3s linear infinite;
  will-change: box-shadow, background-position;
}
@keyframes sweetMomentGlow {
  0% {
    color: #ff6ec7;
    text-shadow: 0 0 8px rgba(255, 110, 199, 0.6);
    transform: translateX(-50%) scale(1);
  }
  100% {
    color: #fff;
    text-shadow: 0 0 12px rgba(255, 110, 199, 0.8);
    transform: translateX(-50%) scale(1.05);
  }
}
@keyframes scoreGlow {
  0% {
    box-shadow: 0 0 8px #cf5cdb;
  }
  100% {
    box-shadow: 0 0 15px #ff6ec7, 0 0 25px rgba(255, 110, 199, 0.5);
  }
}
@keyframes gradientShift {
  0% {
    background-position: 0% 50%;
  }
  100% {
    background-position: 200% 50%;
  }
}
.warp .page .game_area canvas {
  width: 100%;
  height: 100%;
}
.warp .bj2 {
  background-image: url(../img/bj2.jpg);
}
.blur {
  filter: blur(1vw);
}
.fc {
  justify-content: center;
}
.area {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  z-index: 2;
  background: url(../img/area.png) no-repeat center center / 100% 100%;
}
.area .back {
  position: absolute;
  bottom: -4vw;
  width: 40vw;
  margin-right: 4vw;
}
.area .back2 {
  position: absolute;
  bottom: -6vw;
  width: 40vw;
}
.area .submit {
  position: absolute;
  bottom: -15vw;
  width: 40vw;
}
.area .rule {
  width: 100%;
  padding: 0 6vw;
  margin: 17vw 0 10vw;
  flex: 1;
  overflow-y: auto;
  line-height: 1.5;
  text-align: justify;
  letter-spacing: -0.1vw;
  position: relative;
}
.area .prize {
  margin-top: 13vw;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.area .prize .mt5 {
  margin-top: 2vw;
}
.area .prize .info {
  padding: 4vw 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  min-height: 16vw;
  width: 70vw;
}
.area .prize .info:first-child {
  border-bottom: 1px dashed #f3e3bb;
}
.area .prize .info .p2 {
  font-size: 5vw;
  line-height: 7vw;
  max-width: 75vw;
  text-align: center;
}
.area .prize .info .jptit {
  width: 29.467vw;
  margin-bottom: 2vw;
}
.area .prize .edit {
  width: 26vw;
}
.area .form {
  width: 100%;
  padding: 28vw 5vw 0;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.area .form .form-item {
  margin-left: 0;
  margin-bottom: 7vw;
  display: flex;
  align-items: center;
}
.area .form .form-item label {
  width: 24vw;
  font-weight: bold;
  font-size: 5vw;
  white-space: nowrap;
  color: #000;
  flex-shrink: 0;
}
.area .form .form-item div input {
  margin-bottom: 3vw;
}
.area .form .form-item div input:nth-last-child(1) {
  margin-bottom: 0;
}
.area .form .form-item .right {
  flex-shrink: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.area .form .form-item input {
  margin-left: 0.8vw;
  padding-left: 2.5333vw;
  width: 46vw;
  height: 7.7333vw;
  border: 1px #000 solid;
  flex-shrink: 0;
  opacity: 1;
  color: #000;
  font-size: 5vw;
}
.area .form .form-item input::-webkit-input-placeholder {
  color: #000;
  opacity: 0.6;
}
.area .form .form-item input:-moz-placeholder {
  color: #000;
  opacity: 0.6;
}
.area .form .form-item input::-moz-placeholder {
  color: #000;
  opacity: 0.6;
}
.area .form .form-item input:-ms-input-placeholder {
  color: #000;
  opacity: 0.6;
}
.area .form .form-item #getArea {
  opacity: 0;
  position: absolute;
  z-index: -1;
}
.area .form .form-footer {
  margin-top: 2vw;
  display: flex;
  color: #000;
}
.area .form .form-footer p {
  font-size: 3vw;
  line-height: 1.5;
  text-align: justify;
}
.area .form .form-footer p span {
  font-weight: bold;
  font-size: 3vw;
}
.area .form .button {
  margin-top: -5vw;
  width: 30.4vw;
}
.area .form .fs {
  align-items: flex-start;
}
.area .form .fs label {
  margin-top: 0.5vw;
}
.area1 {
  margin-top: 1vw;
  width: 90.2667vw;
  height: 115.8667vw;
  background: url(../img/area1.png) no-repeat center center / 100% 100%;
}
.area2 {
  margin-top: 1vw;
  width: 88.5333vw;
  height: 112.5333vw;
  background: url(../img/area2.png) no-repeat center center / 100% 100%;
}
.area3 {
  margin-top: 1vw;
  width: 85.6vw;
  height: 136.1333vw;
  background: url(../img/area3.png) no-repeat center center / 100% 100%;
}
.mask {
  z-index: 10;
  position: fixed;
  top: 0;
  left: 0;
  min-height: 100vh;
  width: 100vw;
  display: flex;
  flex-direction: column;
  align-items: center;
  background: rgba(255, 255, 255, 0.5);
  transform: translateX(-50%);
  left: 50%;
  color: #000;
}
.mask .countdown {
  height: 59.2vw;
}
.mask .popup {
  margin-top: -1vw;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: relative;
}
.mask .popup .back {
  width: 40vw;
  position: absolute;
  bottom: -6vw;
}
.mask .popup1 {
  width: 85.7333vw;
  height: 56.6667vw;
  background: url(../img/popup1.png) no-repeat center top / 100% 100%;
}
.mask .popup2 {
  width: 85.8667vw;
  height: 65.6vw;
  background: url(../img/popup2.png) no-repeat center top / 100% 100%;
}
.mask .popup2 .close2 {
  width: 8.2667vw;
  position: absolute;
  top: -3vw;
  right: -3vw;
}
.mask .popup3 {
  width: 85.7333vw;
  height: 99.6vw;
  background: url(../img/popup3.png) no-repeat center top / 100% 100%;
}
.mask .popup3 .back {
  width: 32.5333vw;
  bottom: -10vw;
}
.mask .popup4 {
  width: 85.7333vw;
  height: 49.4667vw;
  background: url(../img/popup4.png) no-repeat center top / 100% 100%;
}
.mask .popup5 {
  width: 85.733vw;
  height: 63.733vw;
  background: url(../img/popup5.png) no-repeat center top / 100% 100%;
  padding-top: 15vw;
}
.mask .popup5 .p3 {
  font-size: 8.2667vw;
  white-space: nowrap;
  font-weight: bold;
}
.mask .popup5 .p4 {
  font-size: 6.8vw;
  white-space: nowrap;
}
.mask .popup6 {
  width: 85.733vw;
  height: 63.733vw;
  background: url(../img/popup6.png) no-repeat center top / 100% 100%;
}
@keyframes scaleLight {
  0%,
  100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.6);
    opacity: 0;
  }
}

/* 对话选择界面样式 */
.dialog-choice {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 100;
}

.dialog-box {
  position: relative;
  width: 80vw;
  max-width: 400px;
}

.select-box-bg {
  width: 100%;
  height: auto;
}

.dialog-content {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  padding: 8vw;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.npc-info {
  display: flex;
  align-items: center;
  margin-bottom: 4vw;
}

.npc-avatar {
  width: 12vw;
  height: 12vw;
  margin-right: 3vw;
  border-radius: 50%;
}

.npc-desc {
  color: #8B4513;
  font-size: 3vw;
  font-weight: bold;
}

.dialog-title {
  color: #2C1810;
  font-size: 3.5vw;
  line-height: 1.4;
  margin-bottom: 4vw;
  text-align: center;
  white-space: pre-line;
}

.dialog-options {
  display: flex;
  flex-direction: column;
  gap: 2vw;
}

.dialog-option {
  background: linear-gradient(135deg, #D4AF37, #FFD700);
  border: 2px solid #B8860B;
  border-radius: 2vw;
  padding: 3vw 4vw;
  color: #2C1810;
  font-size: 3vw;
  font-weight: bold;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  white-space: pre-line;
  line-height: 1.3;
}

.dialog-option:hover {
  background: linear-gradient(135deg, #FFD700, #FFA500);
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

.dialog-option:active {
  transform: translateY(0);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}
