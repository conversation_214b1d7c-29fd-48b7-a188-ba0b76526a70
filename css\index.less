// 取消微信浏览器点击的蓝色背景
// *{
//     -webkit-touch-callout:none;
//     -webkit-user-select:none; 
//     -moz-user-select:none;
//     -ms-user-select:none;
//     user-select:none;
//     -webkit-tap-highlight-color: rgba(255, 255, 255, 0);
//     -webkit-user-select: none;
//     -moz-user-focus: none;
//     -moz-user-select: none
// }

@font-face {
    font-family: '思源宋体';
    src: url(https://ztimg.hefei.cc/static/common/fonts/思源宋体.otf);
}

// @font-face {
//     font-family: 'TBMCYXT';
//     src: url(https://ztimg.hefei.cc/static/common/fonts/TaoBaoMaiCaiTi-Regular.ttf);
// }

body,
div,
h1,
h2,
h3,
h4,
h5,
h6,
html,
li,
p,
span {
    font-size: 3.5vw
}

@formcolor:#000;


.musicbtn {
    width: 10vw;
    height: 10vw;
    position: absolute;
    top: 3.6vw;
    right: 2.8vw;
    background: url(../img/music.png) no-repeat center center/contain;
    z-index: 11;
}
.logo{
    width: 18.4vw;
    position: absolute;
    top: 3.6vw;
    left: 2.8vw;
    z-index: 11;
}

.warp {
    margin: 0 auto;
    min-height: 170vw;
    height: 100vh;
    width: 100vw;
    display: flex;
    justify-content: center;
    align-items: center;
    font-family: '思源宋体';
    background: url(../img/bj2.jpg) no-repeat center center/100% 100%;
    position: relative;

    .swipe_container {
        width: 100%;
        height: 100%;
    }

    .page {
        width: 100%;
        height: 100%;
        overflow: hidden;
        display: flex;
        flex-direction: column;
        align-items: center;
        position: relative;
        z-index: 2;
        color: #f3e3bb;
        background: url(../img/bj.jpg) no-repeat center center/100% auto;

        .rotation {
            margin-top: 10vw;
            width: 60vw;
            height: 7vw;
            border-radius: 3.5vw;
            overflow: hidden;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            z-index: 3;

            .van-swipe {
                height: 8.8vw;

                .van-swipe-item {
                    display: flex;
                    flex-direction: column;
                    justify-content: center;
                    align-items: center;
                }
            }

            p {
                text-align: center;
                white-space: nowrap;
                font-size: 4vw;

                span {
                    font-size: 4vw;
                }
            }
        }

        .title {
            margin-top: 2vw;
            width: 79.2vw;
            z-index: 2;
        }

        .title2 {
            margin-top: 3vw;
            width: 60vw;
            z-index: 3;
        }

        .start {
            margin-top: 56vw;
            width: 22vw;
            height: 22vw;
            flex-shrink: 0;
            background: url(../img/start.png) no-repeat center center/100% 100%;
            z-index: 2;
            &::before {
                content: '';
                position: absolute;
                top: -10%;
                left: -10%;
                width: 120%;
                height: 120%;
                background: url(../img/start_area.png) no-repeat center center/100% 100%;
                // 放大光圈动画
                animation: scaleLight 1s ease-in-out infinite;
            }
        }

        .bg2 {
            width: 100vw;
            position: absolute;
            left: 0;
            bottom: 0;
            pointer-events: none;
        }

        .bg3 {
            width: 100vw;
            position: absolute;
            left: 0;
            top: 0;
            pointer-events: none;
        }

        .button_container {
            position: relative;
            z-index: 2;
            margin-top: 5vw;
            display: flex;
            justify-content: space-between;
            width: 100%;
            padding: 0 6vw;

            .button {
                width: 32.2667vw;
            }
        }
        .game_area{
            width: 100vw;
            height: 177.8667vw;
            // position: relative;
            display: flex;
            justify-content: center;
            align-items: center;
            .gameInfo{
                position: absolute;
                top: 15vw;
                left: 2vw;
                color: #000;
            }
            .time{
                width: 18.5333vw;
                height: 20vw;
                background: url(../img/time.png) no-repeat center center/100% 100%;
                color: #000;
                position: absolute;
                top: 10vw;
                display: flex;
                justify-content: center;
                align-items: center;
                font-size: 6vw;
                font-weight: bold;
                padding-bottom: 3vw;
            }
            .score_area{
                width: 84vw;
                height: 4.267vw;
                border: 1px solid #f1e26f;
                box-shadow: 0px 0px 10px 0px rgba(255, 255, 255, 0.5);
                border-radius: 2.1333vw;
                position: absolute;
                top: 22vw;
                display: flex;
                align-items: center;

                // 甜润时刻文字
                &::before{
                    content: '甜润时刻';
                    position: absolute;
                    top: -8vw;
                    left: 50%;
                    transform: translateX(-50%);
                    font-size: 4.5vw;
                    font-weight: bold;
                    color: #ff6ec7;
                    text-shadow: 0 0 8px rgba(255, 110, 199, 0.6);
                    opacity: 0;
                    pointer-events: none;
                    transition: opacity 0.3s ease;
                    z-index: 2;
                    will-change: opacity, transform; // 优化GPU加速
                }
                &::after{
                    content: '';
                    width: 9.3333vw;
                    height: 9.3333vw;
                    background: url(../img/qiu.png) no-repeat center center/100% 100%;
                    position: absolute;
                    right: -4.6667vw;
                    transition: .3s;
                    filter: grayscale(100%);
                }

                // 当进度条满时显示甜润时刻文字
                &.full::before{
                    opacity: 1;
                    animation: sweetMomentGlow 2.5s ease-in-out infinite alternate;
                }
                &.full::after{
                    filter: grayscale(0%);
                    // animation: rott 6s linear infinite;
                }

                .score{
                    background-color: #cf5cdb;
                    position: absolute;
                    left:0;
                    height: 100%;
                    border-radius: 2.1333vw;
                    display: flex;
                    align-items: center;
                    transition: .3s;

                    // 当进度条满的时候的闪光效果（优化性能版本）
                    &.full{
                        background: linear-gradient(90deg, #cf5cdb, #ff6ec7, #cf5cdb);
                        background-size: 200% 100%;
                        animation: scoreGlow 2s ease-in-out infinite alternate,
                                   gradientShift 3s linear infinite;
                        will-change: box-shadow, background-position; // 优化GPU加速
                    }
                }
            }

            // 甜润时刻文字发光动画（优化性能版本）
            @keyframes sweetMomentGlow {
                0% {
                    color: #ff6ec7;
                    text-shadow: 0 0 8px rgba(255, 110, 199, 0.6);
                    transform: translateX(-50%) scale(1);
                }
                100% {
                    color: #fff;
                    text-shadow: 0 0 12px rgba(255, 110, 199, 0.8);
                    transform: translateX(-50%) scale(1.05);
                }
            }

            // 进度条发光动画（优化性能版本）
            @keyframes scoreGlow {
                0% {
                    box-shadow: 0 0 8px #cf5cdb;
                }
                100% {
                    box-shadow: 0 0 15px #ff6ec7, 0 0 25px rgba(255, 110, 199, 0.5);
                }
            }

            // 渐变背景移动动画（优化性能版本）
            @keyframes gradientShift {
                0% {
                    background-position: 0% 50%;
                }
                100% {
                    background-position: 200% 50%;
                }
            }
            canvas{
                width: 100%;
                height: 100%;
            }
        }

    }

    .bj2 {
        background-image: url(../img/bj2.jpg);
    }
}

.blur {
    filter: blur(1vw);
}

.fc {
    justify-content: center;
}

.area {
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
    z-index: 2;
    background: url(../img/area.png) no-repeat center center/100% 100%;

    .back {
        position: absolute;
        bottom: -4vw;
        width: 40vw;
        margin-right: 4vw;
    }
    .back2 {
        position: absolute;
        bottom: -6vw;
        width: 40vw;
    }

    .submit {
        position: absolute;
        bottom: -15vw;
        width: 40vw;
    }

    .rule {
        width: 100%;
        padding: 0 6vw;
        margin: 17vw 0 10vw;
        flex: 1;
        overflow-y: auto;
        line-height: 1.5;
        text-align: justify;
        letter-spacing: -0.1vw;
        position: relative;
    }

    .prize {
        margin-top: 13vw;
        display: flex;
        flex-direction: column;
        align-items: center;

        .mt5 {
            margin-top: 2vw;
        }

        .info {
            padding: 4vw 0;
            display: flex;
            flex-direction: column;
            align-items: center;
            min-height: 16vw;
            width: 70vw;

            &:first-child {
                border-bottom: 1px dashed #f3e3bb;
            }

            .p2 {
                font-size: 5vw;
                line-height: 7vw;
                max-width: 75vw;
                text-align: center;
            }

            .jptit {
                width: 29.467vw;
                margin-bottom: 2vw;
            }
        }

        .edit {
            width: 26vw;
        }
    }


    .form {
        width: 100%;
        padding: 28vw 5vw 0;
        display: flex;
        flex-direction: column;
        align-items: center;

        .form-item {
            margin-left: 0;
            margin-bottom: 7vw;
            display: flex;
            align-items: center;

            label {
                width: 24vw;
                font-weight: bold;
                font-size: 5vw;
                white-space: nowrap;
                color: @formcolor;
                flex-shrink: 0;
            }

            div {

                input {
                    margin-bottom: 3vw;

                }

                input:nth-last-child(1) {
                    margin-bottom: 0;
                }
            }

            .right {
                flex-shrink: 0;
                display: flex;
                flex-direction: column;
                align-items: center;
            }

            input {
                margin-left: 0.8vw;
                padding-left: 2.5333vw;
                width: 46vw;
                height: 7.7333vw;
                border: 1px @formcolor solid;
                flex-shrink: 0;
                // border-radius: 0.8vw;
                opacity: 1;
                color: @formcolor;
                font-size: 5vw;

                &::-webkit-input-placeholder {
                    color: @formcolor;
                    opacity: 0.6;
                }

                &:-moz-placeholder {
                    color: @formcolor;
                    opacity: 0.6;
                }

                &::-moz-placeholder {
                    color: @formcolor;
                    opacity: 0.6;
                }

                &:-ms-input-placeholder {
                    color: @formcolor;
                    opacity: 0.6;
                }
            }

            #getArea {
                opacity: 0;
                position: absolute;
                z-index: -1;
            }

        }

        .form-footer {
            margin-top: 2vw;
            display: flex;
            color: @formcolor;

            p {
                font-size: 3vw;
                line-height: 1.5;
                text-align: justify;
                span{
                    font-weight: bold;
                    font-size: 3vw;
                }
            }
        }

        .button {
            margin-top: -5vw;
            width: 30.4vw;
        }

        .fs {
            align-items: flex-start;

            label {
                margin-top: 0.5vw;
            }
        }
    }
}

.area1 {
    margin-top: 1vw;
    width: 90.2667vw;
    height: 115.8667vw;
    background: url(../img/area1.png) no-repeat center center/100% 100%;
}

.area2 {
    margin-top: 1vw;
    width: 88.5333vw;
    height: 112.5333vw;
    background: url(../img/area2.png) no-repeat center center/100% 100%;
}

.area3 {
    margin-top: 1vw;
    width: 85.6vw;
    height: 136.1333vw;
    background: url(../img/area3.png) no-repeat center center/100% 100%;
}

.mask {
    z-index: 10;
    position: fixed;
    top: 0;
    left: 0;
    min-height: 100vh;
    width: 100vw;
    display: flex;
    flex-direction: column;
    align-items: center;
    // background: rgba(18, 45, 29, 0.3);
    // backdrop-filter: blur(4px);
    background: rgba(255, 255, 255, 0.5);
    transform: translateX(-50%);
    left: 50%;
    color: #000;
    .countdown{
        height: 59.2vw;
    }
    .popup {
        margin-top: -1vw;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        position: relative;

        .back {
            width: 40vw;
            position: absolute;
            bottom: -6vw;
        }

    }

    .popup1 {
        width: 85.7333vw;
        height: 56.6667vw;
        background: url(../img/popup1.png) no-repeat center top / 100% 100%;
    }

    .popup2 {
        width: 85.8667vw;
        height: 65.6vw;
        background: url(../img/popup2.png) no-repeat center top / 100% 100%;

        .close2 {
            width: 8.2667vw;
            position: absolute;
            top: -3vw;
            right: -3vw;
        }
    }

    .popup3 {
        width: 85.7333vw;
        height: 99.6vw;
        background: url(../img/popup3.png) no-repeat center top / 100% 100%;
        .back{
            width: 32.5333vw;
            bottom: -10vw;
        }
    }

    .popup4 {
        width: 85.7333vw;
        height: 49.4667vw;
        background: url(../img/popup4.png) no-repeat center top / 100% 100%;
    }

    .popup5 {
        width: 85.733vw;
        height: 63.733vw;
        background: url(../img/popup5.png) no-repeat center top / 100% 100%;
        padding-top: 15vw;

        .p3 {
            font-size: 8.2667vw;
            white-space: nowrap;
            font-weight: bold;
        }

        .p4 {
            font-size: 6.8vw;
            white-space: nowrap;
        }
    }

    .popup6 {
        width: 85.733vw;
        height: 63.733vw;
        background: url(../img/popup6.png) no-repeat center top / 100% 100%;
    }
}

@keyframes scaleLight{
    0%, 100%{
        transform: scale(1);
        opacity: 1;
    }
    50%{
        transform: scale(1.6);
        opacity: 0;
    }
}